import { NextRequest, NextResponse } from "next/server";
import { UserRole } from "./src/types/roles";

// Route permissions mapping based on data.ts
const ROUTE_PERMISSIONS: Record<string, UserRole[]> = {
  "/grupos-empresariais": ["ADMIN_SDG", "CONTROLLER_SDG", "ATTENDANT_SDG"],
  "/gestao-de-unidades": ["ADMIN_SDG", "CONTROLLER_SDG", "ATTENDANT_SDG"],
  "/contabilidades": ["ADMIN_SDG", "CONTROLLER_SDG"],
  "/sindicatos": ["ADMIN_SDG", "CONTROLLER_SDG"],
  "/usuarios": [], // No role restrictions based on data.ts
  "/faturamento": [], // No role restrictions based on data.ts
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = ["/", "/esqueci-senha", "/redefinir-senha"];

// Protected routes pattern
const PROTECTED_ROUTES_PATTERN =
  /^\/(?:grupos-empresariais|gestao-de-unidades|contabilidades|sindicatos|usuarios|faturamento)/;

function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some((route) => {
    if (route === "/") return pathname === "/";
    return pathname.startsWith(route);
  });
}

function getUserFromCookies(
  request: NextRequest
): { role: UserRole; id: string } | null {
  try {
    // Try to get user data from cookies
    const userCookie = request.cookies.get("@gestao-sindicatos:user")?.value;
    const tokenCookie = request.cookies.get(
      "@gestao-sindicatos:access-token"
    )?.value;

    if (userCookie && tokenCookie) {
      // Decode URL-encoded cookie value
      const decodedUserData = decodeURIComponent(userCookie);
      const userData = JSON.parse(decodedUserData);
      if (userData.role && userData.id) {
        return {
          role: userData.role as UserRole,
          id: userData.id,
        };
      }
    }
  } catch (error) {
    console.error("Error parsing user from cookies:", error);
  }

  return null;
}

function hasPermission(userRole: UserRole, pathname: string): boolean {
  const requiredRoles = ROUTE_PERMISSIONS[pathname];

  // If no roles are specified, allow access to all authenticated users
  if (!requiredRoles || requiredRoles.length === 0) {
    return true;
  }

  return requiredRoles.includes(userRole);
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes
  if (isPublicRoute(pathname)) {
    // If user is authenticated and trying to access login page, redirect to default page
    if (pathname === "/") {
      const user = getUserFromCookies(request);
      if (user) {
        const defaultUrl = new URL("/sindicatos", request.url);
        return NextResponse.redirect(defaultUrl);
      }
    }
    return NextResponse.next();
  }

  // Check if this is a protected route
  if (PROTECTED_ROUTES_PATTERN.test(pathname)) {
    const user = getUserFromCookies(request);

    // No user data - let client-side AuthGuard handle the redirect
    // This allows for proper loading states and client-side token validation
    if (!user) {
      return NextResponse.next();
    }

    // Check role permissions
    if (!hasPermission(user.role, pathname)) {
      // Redirect to access denied page or default allowed page
      const accessDeniedUrl = new URL("/sindicatos", request.url);
      return NextResponse.redirect(accessDeniedUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
