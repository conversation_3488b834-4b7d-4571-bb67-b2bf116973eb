import { IRegister } from "@/http/auth/types";
import { LabelAndValue } from "@/types/labelAndValue";

type ObjectType = {
  id: string;
  name?: string;
  alias?: string;
};

export function convertToLabelAndValue(data: ObjectType[]): LabelAndValue[] {
  return data.map((item) => ({
    label: item.name || "",
    value: item.id,
  }));
}

export function convertToStringArray(data: LabelAndValue[]): string[] {
  return data.map((item) => item.value);
}

export function clearSpecialCaracters(str: string) {
  return str.replace(/[^a-zA-Z0-9]/g, "");
}

export const verifyIsAdmin = (role: IRegister["role"]) => {
  return (
    role === "CONTROLLER_SDG" ||
    role === "ADMIN_SDG" ||
    role === "FINANCIAL_SDG"
  );
};
