import { Input } from "@/components/ui/input";
import { Controller, useFormContext } from "react-hook-form";
import { maskPhoneBR } from "@/lib/format";
import { companiesGroupsValidationType } from "./validations";

export function SecondStep() {
  const {
    control,
    formState: { errors },
    register,
  } = useFormContext<companiesGroupsValidationType>();

  return (
    <div className="flex flex-col gap-4">
      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5">
        Contato principal
      </span>
      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome"
          errorMessage={errors?.contact?.name?.message as string}
          {...register("contact.name")}
        />

        <Controller
          name="contact.phone"
          control={control}
          render={({ field }) => (
            <Input
              label="Telefone"
              placeholder="Digite o telefone"
              value={maskPhoneBR(field.value || "")}
              onChange={(e) => {
                const rawValue = e.target.value.replace(/\D/g, "");
                field.onChange(rawValue);
              }}
              errorMessage={errors?.contact?.phone?.message as string}
            />
          )}
        />

        <Input
          label="Email"
          placeholder="Digite o email"
          errorMessage={errors?.contact?.email?.message as string}
          {...register("contact.email")}
        />
      </div>

      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-8">
        Contato financeiro
      </span>

      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome"
          errorMessage={errors?.finance?.name?.message as string}
          {...register("finance.name")}
        />

        <Controller
          name="finance.phone"
          control={control}
          render={({ field }) => (
            <Input
              label="Telefone"
              placeholder="Digite o telefone"
              value={maskPhoneBR(field.value || "")}
              onChange={(e) => {
                const rawValue = e.target.value.replace(/\D/g, "");
                field.onChange(rawValue);
              }}
              errorMessage={errors?.finance?.phone?.message as string}
            />
          )}
        />

        <Input
          label="Email"
          placeholder="Digite o email"
          errorMessage={errors?.finance?.email?.message as string}
          {...register("finance.email")}
        />
      </div>
    </div>
  );
}
