"use client";

import { Modal } from "@/components/modals";
import { But<PERSON> } from "@/components/ui/button";

import { SuccessModal } from "@/components/modals/success-modal";
import {
  createBusinessGroup,
  updateBusinessGroup,
} from "@/http/business-groups";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { FirstStep } from "./firstStep";
import { SecondStep } from "./secondStep";
import {
  companiesGroupsValidation,
  companiesGroupsValidationType,
} from "./validations";
import { CompaniesGroup } from "@/types/companies-groups";

type SindicatoModalFormProps = {
  isEditMode?: boolean;
  triggerButton: React.ReactNode;
  id?: string;
  prefill?: CompaniesGroup;
};

export function CompaniesGroupModalForm({
  triggerButton,
  isEditMode,
  id,
  prefill,
}: SindicatoModalFormProps) {
  const { toast } = useToast();
  const [step, setStep] = useState<number>(1);
  const [isOpen, setIsOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successTitle, setSuccessTitle] = useState("");

  const queryClient = useQueryClient();
  const form = useForm<companiesGroupsValidationType>({
    resolver: zodResolver(companiesGroupsValidation),
    mode: "all",
    defaultValues:
      isEditMode && prefill
        ? {
            union: prefill.union
              ? {
                  label: prefill.union.alias || "",
                  value: prefill.union.id || "",
                }
              : undefined,
            consultancy: prefill.consultancy
              ? {
                  label: prefill.consultancy.alias || "",
                  value: prefill.consultancy.id || "",
                }
              : undefined,
            cnpj: prefill.cnpj,
            fantasyName: prefill.alias,
            socialReason: prefill.name,
            addressObj: prefill.address,
            cnae: prefill.cnae,
            address: prefill.address
              ? `${prefill.address.street}, ${prefill.address.number}\n${
                  prefill.address.complement || ""
                }\n${prefill.address.neighborhood}\n${prefill.address.city}, ${
                  prefill.address.state
                }`
              : undefined,
            contact: prefill.contact
              ? {
                  name: prefill.contact.name || "",
                  phone: prefill.contact.phone || "",
                  email: prefill.contact.email || "",
                }
              : undefined,
            finance: prefill.financeContact
              ? {
                  name: prefill.financeContact.name || undefined,
                  phone: prefill.financeContact.phone || undefined,
                  email: prefill.financeContact.email || undefined,
                }
              : undefined,
          }
        : {
            union: undefined,
            consultancy: undefined,
          },
  });

  const handleNext = async () => {
    if (step === 1) {
      const isValid = await form.trigger(
        ["union", "cnpj", "fantasyName", "socialReason", "address"],
        { shouldFocus: true }
      );
      if (!isValid) return;
      form.clearErrors(["contact", "finance"]);
      setStep(2);
      return;
    }
  };

  const handleConclude = async () => {
    const isValid = await form.trigger(["contact", "finance"]);
    if (!isValid) return;
    await handleSubmit(form.getValues());
  };

  const { mutate: createGroupMutation, isPending: isCreatingGroup } =
    useMutation({
      mutationFn: (data: companiesGroupsValidationType) =>
        createBusinessGroup({
          unionId: data.union.value,
          consultancyId: data.consultancy ? data.consultancy.value : undefined,
          cnpj: String(data.cnpj),
          alias: data.fantasyName as string,
          name: data.socialReason as string,
          address: {
            street: data.addressObj?.street || "",
            number: data.addressObj?.number || "",
            complement: data.addressObj?.complement || "",
            zip: data.addressObj?.zip || "",
            neighborhood: data.addressObj?.neighborhood || "",
            city: data.addressObj?.city || "",
            state: data.addressObj?.state || "",
          },
          cnae: String((data as unknown as { cnae?: string }).cnae || ""),
          contact: data.contact,
          financeContact: data.finance,
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["business-groups"] });
        setIsOpen(false);
        setShowSuccessModal(true);
        setStep(1);
        form.reset();
        setSuccessTitle("Grupo empresarial cadastrado com sucesso!");
      },
      onError: (error: AxiosError<Error>) => {
        toast({
          title: "Erro ao cadastrar grupo empresarial.",
          description: error.response?.data.message,
          variant: "destructive",
        });
      },
    });

  const { mutate: updateGroupMutation, isPending: isUpdatingGroup } =
    useMutation({
      mutationFn: (data: companiesGroupsValidationType) =>
        updateBusinessGroup({
          id: id as string,
          alias: data.fantasyName,
          contact: data.contact,
          financeContact: data.finance,
          consultancyId: data.consultancy ? data.consultancy.value : undefined,
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["business-groups"] });
        setIsOpen(false);
        setShowSuccessModal(true);
        setStep(1);
        form.reset();
        setSuccessTitle("Grupo empresarial atualizado com sucesso!");
      },
      onError: (error: AxiosError<Error>) => {
        toast({
          title: "Erro ao editar grupo empresarial.",
          description: error.response?.data.message,
          variant: "destructive",
        });
      },
    });

  const handleSubmit = async (data: companiesGroupsValidationType) => {
    if (isEditMode && id) {
      updateGroupMutation(data);
      return;
    }
    createGroupMutation(data);
  };

  const isLoading = isCreatingGroup || isUpdatingGroup;

  return (
    <>
      <Modal
        title={
          isEditMode ? "Editar grupo empresarial" : "Novo grupo empresarial"
        }
        description={`Etapa ${step} de 2`}
        triggerButton={triggerButton}
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) {
            setStep(1);
            form.reset();
          }
        }}
      >
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-16"
            onSubmit={(e) => e.preventDefault()}
          >
            {step === 1 && <FirstStep isEditMode={isEditMode} />}
            {step === 2 && <SecondStep />}
            <div className="flex gap-4">
              {step !== 1 && (
                <Button
                  variant="outline"
                  className="w-full font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
                  onClick={() => setStep(step - 1)}
                >
                  Voltar
                </Button>
              )}
              {step < 2 ? (
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleNext}
                >
                  Continuar
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleConclude}
                  isLoading={isLoading}
                  disabled={isLoading}
                >
                  Concluir
                </Button>
              )}
            </div>
          </form>
        </FormProvider>
      </Modal>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={successTitle}
      />
    </>
  );
}
