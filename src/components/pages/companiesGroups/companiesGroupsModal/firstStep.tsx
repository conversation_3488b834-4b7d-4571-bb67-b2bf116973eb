import { Combobox } from "@/components/ui/combobox";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useCnpj } from "@/hooks/use-cnpj";
import { getConsultancies } from "@/http/consultancies";
import { ExternalCompanyData } from "@/http/external";
import { getUnions } from "@/http/unions";
import { maskCNPJ, unformatCNPJ } from "@/lib/format";
import { useQuery } from "@tanstack/react-query";
import { Controller, useFormContext } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";

type FirstStepProps = { isEditMode?: boolean };

export function FirstStep({ isEditMode }: FirstStepProps) {
  const { toast } = useToast();
  const {
    control,
    formState: { errors },
    watch,
    setValue,
    resetField,
  } = useFormContext();

  const cnpjValue = watch("cnpj") || "";
  const { handleSubmit, isPending } = useCnpj({
    cnpj: cnpjValue,
    onSuccess: (data: ExternalCompanyData) => {
      setValue("socialReason", data.name);
      setValue("fantasyName", data.alias);
      setValue("addressObj.street", data.address.street);
      setValue("addressObj.number", data.address.number);
      setValue("addressObj.complement", data.address.complement);
      setValue("addressObj.zip", data.address.zip);
      setValue("addressObj.neighborhood", data.address.neighborhood);
      setValue("addressObj.city", data.address.city);
      setValue("addressObj.state", data.address.state);
      setValue(
        "address",
        `${data.address.street}, ${data.address.number}\n${
          data.address.complement || ""
        }\n${data.address.neighborhood}\n${data.address.city}, ${
          data.address.state
        }\n${data.address.zip}`
      );
      setValue("cnae", data.cnae);
    },
    onError: () => {
      resetField("addressObj");
      resetField("cnae");
      toast({
        title: "Erro ao buscar dados da empresa ou esse CNPJ não existe",
        variant: "destructive",
      });
    },
  });

  const { data: unionsData, isFetching: isFetchingUnions } = useQuery({
    queryKey: ["unions"],
    queryFn: () =>
      getUnions({
        page: 1,
        limit: 999,
      }),
    refetchOnWindowFocus: false,
  });

  const { data: consultanciesData, isFetching: isFetchingConsultancies } =
    useQuery({
      queryKey: ["consultancies"],
      queryFn: () =>
        getConsultancies({
          page: 1,
          limit: 999,
        }),
      refetchOnWindowFocus: false,
    });

  return (
    <div className="flex flex-col gap-8">
      <Controller
        name="union"
        control={control}
        render={({ field }) => (
          <Combobox
            label="Sindicato vinculado"
            placeholder="Selecione o sindicato"
            isLoading={isFetchingUnions}
            options={
              unionsData?.unions.map((union) => {
                return {
                  label: union.alias,
                  value: String(union.id ?? ""),
                };
              }) || []
            }
            onChange={field.onChange}
            disabled={isEditMode}
            value={field.value || undefined}
            errorMessage={errors?.union?.message as string}
          />
        )}
      />
      <Controller
        name="consultancy"
        control={control}
        render={({ field }) => (
          <Combobox
            label="Contabilidade Vinculada"
            placeholder="Selecione a contabilidade"
            isLoading={isFetchingConsultancies}
            options={
              consultanciesData?.consultancies.map((consultancy) => {
                return {
                  label: consultancy.alias ?? consultancy.name ?? "",
                  value: String(consultancy.id ?? ""),
                };
              }) || []
            }
            onChange={field.onChange}
            disabled={isEditMode && !!watch("consultancy")}
            value={field.value || undefined}
            errorMessage={errors?.consultancy?.message as string}
          />
        )}
      />

      <Controller
        name="cnpj"
        control={control}
        render={({ field }) => (
          <Input
            isLoading={isPending}
            label="CNPJ"
            placeholder={isEditMode ? undefined : "Digite o CNPJ"}
            errorMessage={errors.cnpj?.message as string}
            value={maskCNPJ(field.value || "")}
            onChange={(e) => {
              const unmaskedValue = unformatCNPJ(e.target.value);
              field.onChange(unmaskedValue);
            }}
            onBlur={(event) => {
              const value = event.target.value;
              if (value.length < 18) return;
              if (value === "") return;
              handleSubmit();
            }}
            disabled={isEditMode}
            readOnly={isEditMode}
          />
        )}
      />

      <Input
        label="Razão Social"
        value={watch("socialReason") || "Preenchido automaticamente"}
        readOnly
        disabled
        errorMessage={errors.socialReason?.message as string}
      />

      <Input
        label="Nome fantasia"
        value={watch("fantasyName") || "Preenchido automaticamente"}
        readOnly
        disabled
        errorMessage={errors.fantasyName?.message as string}
      />

      <Textarea
        label="Endereço"
        value={watch("address") || "Preenchido automaticamente"}
        readOnly
        disabled
        className="whitespace-pre-wrap"
      />

      <Input
        label="CNAE"
        value={watch("cnae") || "Preenchido automaticamente"}
        readOnly
        disabled
        errorMessage={errors.cnae?.message as string}
      />
    </div>
  );
}
