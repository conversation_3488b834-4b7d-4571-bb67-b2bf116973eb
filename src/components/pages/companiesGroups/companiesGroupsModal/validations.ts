import { INVALID_EMAIL_MESSAGE, REQUIRED_MESSAGE } from "@/validations";
import { z } from "zod";

export const companiesGroupsValidation = z.object({
  cnpj: z
    .string({ required_error: REQUIRED_MESSAGE })
    .nonempty({ message: REQUIRED_MESSAGE }),
  union: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { required_error: REQUIRED_MESSAGE }
  ),
  consultancy: z
    .object(
      { value: z.string(), label: z.string() },
      { message: REQUIRED_MESSAGE }
    )
    .optional(),
  fantasyName: z.string().nonempty({ message: REQUIRED_MESSAGE }).optional(),
  socialReason: z.string().nonempty({ message: REQUIRED_MESSAGE }).optional(),
  address: z.string().nonempty({ message: REQUIRED_MESSAGE }),
  addressObj: z
    .object({
      street: z.string().min(1).optional(),
      number: z.string().min(1).optional(),
      complement: z.string().optional(),
      zip: z.string().min(1).optional(),
      neighborhood: z.string().min(1).optional(),
      city: z.string().min(1).optional(),
      state: z.string().min(1).optional(),
    })
    .optional(),
  cnae: z.string().nonempty({ message: REQUIRED_MESSAGE }).optional(),
  contact: z.object({
    name: z.string().min(1, { message: REQUIRED_MESSAGE }),
    phone: z
      .string({ required_error: REQUIRED_MESSAGE })
      .min(1, { message: REQUIRED_MESSAGE }),
    email: z.string().email(INVALID_EMAIL_MESSAGE),
  }),
  finance: z.object({
    name: z.string().min(1, { message: REQUIRED_MESSAGE }),
    phone: z.string({ required_error: REQUIRED_MESSAGE }).min(1, {
      message: REQUIRED_MESSAGE,
    }),
    email: z.string().email(INVALID_EMAIL_MESSAGE),
  }),
});

export type companiesGroupsValidationType = z.infer<
  typeof companiesGroupsValidation
>;
