import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/selectUi";
import { SearchableMultiSelect } from "@/components/ui/multiselect";
import { Modal } from "@/components/modals";
import { Separator } from "@/components/ui/separator";
import { Trash } from "lucide-react";
import { Controller, useFormContext, useFieldArray } from "react-hook-form";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { getBanks } from "@/http/banks";
import { getPlans, createPlan } from "@/http/plans";
import { formatBRL, parseBRLToNumber, maskCpfCnpj } from "@/lib/format";
import { useDebounce } from "@/hooks/use-debounce";

export function SecondStep() {
  const {
    register,
    control,
    formState: { errors },
  } = useFormContext();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "plans",
    keyName: "fieldKey",
  });

  const queryClient = useQueryClient();
  const [isCreatePlanOpen, setIsCreatePlanOpen] = React.useState(false);
  const [multiSelectResetKey, setMultiSelectResetKey] = React.useState(0);
  const [newPlanName, setNewPlanName] = React.useState("");
  const [createError, setCreateError] = React.useState<string | null>(null);
  const [duplicatePlanError, setDuplicatePlanError] = React.useState<
    string | null
  >(null);
  const debouncedNewPlanName = useDebounce(newPlanName.trim(), 400);

  const { data: banksData } = useQuery({
    queryKey: ["banks"],
    queryFn: async () => {
      const response = await getBanks();
      return response.data;
    },
  });

  const { data: plansList = [], isLoading: isPlansLoading } = useQuery({
    queryKey: ["plans"],
    queryFn: async () => {
      const response = await getPlans();
      return response.data?.plans || [];
    },
  });

  const createPlanMutation = useMutation({
    mutationFn: async (name: string) => {
      const response = await createPlan({ name });
      return response.data?.plan;
    },
    onSuccess: async (created) => {
      await queryClient.invalidateQueries({ queryKey: ["plans"] });
      await queryClient.refetchQueries({ queryKey: ["plans"], type: "active" });
      if (created?.id && created?.name) {
        const createdId = String(created.id);
        const exists = fields.some(
          (f: Record<string, unknown>) =>
            (f as { id?: string }).id === createdId
        );
        if (!exists) append({ id: createdId, value: 0 });
      }
      setIsCreatePlanOpen(false);
      setNewPlanName("");
      setCreateError(null);
      setDuplicatePlanError(null);
      // Força reinicialização do multiselect para limpar busca e exibir todas opções
      setMultiSelectResetKey((k) => k + 1);
    },
    onError: (error: { response?: { data?: { message?: string } } }) => {
      setCreateError(error.response?.data?.message || "Erro ao criar plano");
    },
  });

  // Verifica se já existe plano com o mesmo nome (consulta backend para garantir a lista mais recente)
  React.useEffect(() => {
    let cancelled = false;
    (async () => {
      if (!debouncedNewPlanName) {
        if (!cancelled) setDuplicatePlanError(null);
        return;
      }
      try {
        const latest = await getPlans();
        const list: Array<{ id: string; name: string }> =
          latest.data?.plans || [];
        const exists = list.some(
          (p) =>
            p.name.trim().toLowerCase() === debouncedNewPlanName.toLowerCase()
        );
        if (cancelled) return;
        setDuplicatePlanError(
          exists ? "Já existe um plano com este nome" : null
        );
      } catch {
        // não bloqueia em caso de erro de rede
        if (!cancelled) setDuplicatePlanError(null);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [debouncedNewPlanName]);

  const banks =
    banksData?.banks?.map(
      (bank: { id: string; code: string; name: string }) => ({
        label: `${bank.code} - ${bank.name}`,
        value: bank.id,
      })
    ) || [];

  const plans = (plansList as Array<{ id: string; name: string }>).map(
    (plan) => ({
      label: plan.name,
      value: plan.id,
    })
  );

  // Multi-select derived state
  const selectedPlans = fields
    .map((f: Record<string, unknown>) =>
      plans.find(
        (p: { label: string; value: string }) =>
          p.value === (f as { id?: string }).id
      )
    )
    .filter(Boolean) as { label: string; value: string }[];

  const handlePlansChange = (selected: { label: string; value: string }[]) => {
    const selectedIds = new Set(selected.map((s) => s.value));
    for (let i = fields.length - 1; i >= 0; i -= 1) {
      const currentId = (fields[i] as { id?: string }).id as string;
      if (currentId && !selectedIds.has(currentId)) {
        remove(i);
      }
    }
    selected.forEach((s) => {
      const exists = fields.some(
        (f: Record<string, unknown>) => (f as { id?: string }).id === s.value
      );
      if (!exists) append({ id: s.value, value: 0 });
    });
  };

  // Observação: validação agregada dos planos é exibida no seletor acima

  if (isPlansLoading) {
    return null;
  }

  return (
    <>
      <div className="flex flex-col gap-8">
        <Controller
          name="bankId"
          control={control}
          render={({ field }) => {
            const selected = banks.find(
              (b: { label: string; value: string }) => b.value === field.value
            );
            return (
              <Select
                label="Banco"
                placeholder="Selecione o banco"
                options={banks}
                onChange={(opt) => field.onChange(opt.value)}
                value={selected}
                errorMessage={errors.bankId?.message as string}
              />
            );
          }}
        />

        <Input
          label="Agência"
          placeholder="Digite a agência"
          {...register("agency")}
          errorMessage={errors.agency?.message as string}
        />
        <Input
          label="Conta"
          placeholder="Digite a conta"
          {...register("accountNumber")}
          errorMessage={errors.accountNumber?.message as string}
        />

        <Input
          label="Chave PIX"
          placeholder="Digite a chave PIX"
          {...register("pixKey")}
          errorMessage={errors.pixKey?.message as string}
        />

        <Input
          label="CNPJ/CPF"
          placeholder="Digite CPF ou CNPJ"
          {...register("cpfCnpj", {
            onChange: (e) => {
              e.target.value = maskCpfCnpj(e.target.value);
            },
          })}
          errorMessage={errors?.cpfCnpj?.message as string}
        />

        <Separator />

        <div className="flex flex-col gap-3">
          <SearchableMultiSelect
            key={multiSelectResetKey}
            label="Planos"
            options={plans}
            value={selectedPlans}
            onChange={handlePlansChange}
            searchPlaceholder="Pesquise pelo plano"
            placeholder="Selecione os planos"
            createLabel="+ Criar novo plano"
            errorMessage={errors.plans?.message as string}
            onCreateOption={(name) => {
              setNewPlanName(name);
              setCreateError(null);
              setIsCreatePlanOpen(true);
            }}
          />
        </div>

        {fields.length > 0 && (
          <div className="flex flex-col gap-4">
            {fields.map((field, index) => {
              const planId = (field as unknown as { id?: string }).id as
                | string
                | undefined;
              const planLabel =
                plans.find(
                  (p: { label: string; value: string }) => p.value === planId
                )?.label || "";
              return (
                <div
                  key={(field as unknown as { fieldKey: string }).fieldKey}
                  className="grid grid-cols-12 gap-4 items-end"
                >
                  <div className="col-span-6">
                    <div className="h-11 flex items-center bg-gray-100 rounded-sm px-4 text-gray-700">
                      {planLabel}
                    </div>
                  </div>
                  <div className="col-span-4">
                    <Controller
                      name={`plans.${index}.value` as const}
                      control={control}
                      render={({ field }) => (
                        <Input
                          label="Valor (R$)"
                          placeholder="R$ 0,00"
                          type="text"
                          value={formatBRL(
                            Number.isFinite(Number(field.value))
                              ? Number(field.value)
                              : 0
                          )}
                          onChange={(e) =>
                            field.onChange(parseBRLToNumber(e.target.value))
                          }
                          onBlur={field.onBlur}
                          errorMessage={undefined}
                        />
                      )}
                    />
                  </div>
                  <div className="col-span-2">
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => remove(index)}
                    >
                      <Trash size={16} />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        <Input
          label="Percentual de comissão (%)"
          placeholder="0 a 100"
          type="number"
          min="0"
          max="100"
          step="1"
          {...register("commissionPercentage", {
            setValueAs: (v) => (v === "" || v === null ? undefined : Number(v)),
          })}
          errorMessage={errors.commissionPercentage?.message as string}
        />
        <Input
          label="Dia de pagamento da comissão"
          placeholder="Ex: 5"
          type="number"
          min="1"
          max="31"
          {...register("commissionPaymentDay", {
            setValueAs: (v) => (v === "" || v === null ? undefined : Number(v)),
          })}
          errorMessage={errors.commissionPaymentDay?.message as string}
        />
      </div>
      <Modal
        title="Criar novo plano"
        triggerButton={<span />}
        open={isCreatePlanOpen}
        onOpenChange={setIsCreatePlanOpen}
      >
        <div className="flex flex-col gap-16">
          <Input
            label="Nome do plano"
            placeholder="Informe o nome"
            value={newPlanName}
            onChange={(e) => setNewPlanName(e.target.value)}
            errorMessage={duplicatePlanError || createError || undefined}
          />
          <Button
            onClick={() => {
              if (!newPlanName.trim() || duplicatePlanError) return;
              createPlanMutation.mutate(newPlanName.trim());
            }}
            disabled={
              createPlanMutation.isPending ||
              !newPlanName.trim() ||
              !!duplicatePlanError
            }
            variant="secondary"
            size="lg"
            className="w-full"
          >
            {createPlanMutation.isPending ? "Salvando..." : "Confirmar"}
          </Button>
        </div>
      </Modal>
    </>
  );
}
