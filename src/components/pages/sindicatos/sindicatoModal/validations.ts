import { REQUIRED_MESSAGE, INVALID_EMAIL_MESSAGE } from "@/validations";
import { z } from "zod";

// PIX key validation: accepts Email, Phone (10-11 digits or +country),
// CPF (11 digits), CNPJ (14 digits) or EVP (UUID v4)
const isValidPixKey = (value: string): boolean => {
  const v = (value || "").trim();
  if (!v) return true; // handled by optionality elsewhere
  const digits = v.replace(/\D/g, "");
  const isCpf = digits.length === 11;
  const isCnpj = digits.length === 14;
  const isPhone =
    digits.length === 10 || digits.length === 11 || /^\+\d{7,15}$/.test(v);
  const isEmail = /.+@.+\..+/.test(v);
  const isEvpUuid =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
      v
    );
  return isCpf || isCnpj || isPhone || isEmail || isEvpUuid;
};

const addressSchema = z.object({
  street: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, { message: REQUIRED_MESSAGE }),
  number: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  complement: z.string().optional(),
  zip: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(8, { message: REQUIRED_MESSAGE }),
  neighborhood: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(2, { message: REQUIRED_MESSAGE }),
  city: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(2, { message: REQUIRED_MESSAGE }),
  state: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(2, { message: REQUIRED_MESSAGE })
    .max(2, { message: "Estado deve ter 2 caracteres" }),
});

const contactSchema = z.object({
  name: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, { message: REQUIRED_MESSAGE }),
  phone: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(10, { message: REQUIRED_MESSAGE }),
  email: z
    .string({ required_error: REQUIRED_MESSAGE })
    .email(INVALID_EMAIL_MESSAGE),
});

// Optional finance contact: map empty strings to undefined; if any provided, require all
const emptyToUndefined = (v: unknown) =>
  typeof v === "string" && v.trim() === "" ? undefined : v;

const financeContactSchema = z
  .preprocess(
    (raw) => {
      if (!raw || typeof raw !== "object") return raw;
      const obj = raw as { name?: unknown; phone?: unknown; email?: unknown };
      const name = typeof obj.name === "string" ? obj.name.trim() : "";
      const phone = typeof obj.phone === "string" ? obj.phone.trim() : "";
      const email = typeof obj.email === "string" ? obj.email.trim() : "";
      const allEmpty = !name && !phone && !email;
      // Retorna objeto vazio quando todos vazios para evitar invalid_type
      return allEmpty ? {} : raw;
    },
    z
      .object({
        name: z
          .preprocess(
            emptyToUndefined,
            z.string().min(3, { message: REQUIRED_MESSAGE })
          )
          .optional(),
        phone: z
          .preprocess(
            emptyToUndefined,
            z.string().min(10, { message: REQUIRED_MESSAGE })
          )
          .optional(),
        email: z
          .preprocess(emptyToUndefined, z.string().email(INVALID_EMAIL_MESSAGE))
          .optional(),
      })
      .superRefine((val, ctx) => {
        const anyFilled = Boolean(val.name || val.phone || val.email);
        if (!anyFilled) return;
        if (!val.name) {
          ctx.addIssue({
            code: "custom",
            path: ["name"],
            message: REQUIRED_MESSAGE,
          });
        }
        if (!val.phone) {
          ctx.addIssue({
            code: "custom",
            path: ["phone"],
            message: REQUIRED_MESSAGE,
          });
        }
        if (!val.email) {
          ctx.addIssue({
            code: "custom",
            path: ["email"],
            message: REQUIRED_MESSAGE,
          });
        }
      })
  )
  .optional();

const planSchema = z.object({
  id: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  value: z
    .number({ required_error: REQUIRED_MESSAGE })
    .min(0, { message: "Valor deve ser maior que 0" }),
});

export const sindicatoValidation = z.object({
  cnpj: z
    .string({ required_error: REQUIRED_MESSAGE })
    .regex(/^\d{14}$/, { message: "CNPJ deve ter 14 dígitos numéricos" }),
  alias: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, { message: REQUIRED_MESSAGE }),
  name: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, { message: REQUIRED_MESSAGE }),
  address: addressSchema,
  registrationStatus: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  cnae: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  bankId: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  agency: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  accountNumber: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  cpfCnpj: z
    .string({ required_error: REQUIRED_MESSAGE })
    .transform((v) => v.replace(/\D/g, ""))
    .refine((digits) => digits.length === 11 || digits.length === 14, {
      message: "Informe um CPF (11 dígitos) ou CNPJ (14 dígitos)",
    }),
  pixKey: z
    .string()
    .optional()
    .refine((v) => !v || isValidPixKey(v), {
      message: "Chave PIX inválida",
    }),
  commissionPercentage: z
    .number({ required_error: REQUIRED_MESSAGE })
    .min(0)
    .max(100, { message: "Percentual deve estar entre 0 e 100" }),
  commissionPaymentDay: z
    .number({ required_error: REQUIRED_MESSAGE })
    .min(1)
    .max(31, { message: "Dia deve estar entre 1 e 31" }),

  contact: contactSchema,
  financeContact: financeContactSchema.optional(),
  plans: z
    .array(planSchema)
    .min(1, { message: "Selecione pelo menos um plano" })
    .refine(
      (arr) => arr.every((p) => typeof p.value === "number" && p.value > 0),
      {
        message: "Todos os planos devem ter valor acima de 0",
      }
    ),
});

export type sindicatoValidationType = z.infer<typeof sindicatoValidation>;
