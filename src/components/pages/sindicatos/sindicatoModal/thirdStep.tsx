import { Input } from "@/components/ui/input";
import { FieldErrors, useFormContext } from "react-hook-form";
import { maskPhoneBR } from "@/lib/format";

export function ThirdStep() {
  const { register, formState } = useFormContext();
  const errors = formState.errors as FieldErrors<{
    contact: { name?: string; phone?: string; email?: string };
    financeContact?: { name?: string; phone?: string; email?: string };
  }>;

  // Registers to preserve RHF handlers and add masking while propagating events
  const contactPhoneReg = register("contact.phone");
  const financePhoneReg = register("financeContact.phone", {
    setValueAs: (v) =>
      typeof v === "string" && v.trim() === "" ? undefined : v,
  });

  return (
    <div className="flex flex-col gap-4">
      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5">
        Contato principal
      </span>
      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome completo"
          {...register("contact.name")}
          errorMessage={errors.contact?.name?.message as string}
        />
        <Input
          label="Telefone"
          placeholder="Digite o telefone"
          name={contactPhoneReg.name}
          ref={contactPhoneReg.ref}
          onBlur={contactPhoneReg.onBlur}
          onChange={(e) => {
            e.target.value = maskPhoneBR(e.target.value);
            contactPhoneReg.onChange(e);
          }}
          onInput={(e) => {
            const el = e.target as HTMLInputElement;
            el.value = maskPhoneBR(el.value);
            contactPhoneReg.onChange(
              e as unknown as React.ChangeEvent<HTMLInputElement>
            );
          }}
          errorMessage={errors.contact?.phone?.message as string}
        />
        <Input
          label="Email"
          placeholder="Digite o email"
          {...register("contact.email")}
          errorMessage={errors.contact?.email?.message as string}
        />
      </div>

      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-8">
        Contato financeiro
      </span>

      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome completo"
          {...register("financeContact.name", {
            setValueAs: (v) =>
              typeof v === "string" && v.trim() === "" ? undefined : v,
          })}
          errorMessage={errors.financeContact?.name?.message as string}
        />
        <Input
          label="Telefone"
          placeholder="Digite o telefone"
          name={financePhoneReg.name}
          ref={financePhoneReg.ref}
          onBlur={financePhoneReg.onBlur}
          onChange={(e) => {
            e.target.value = maskPhoneBR(e.target.value);
            financePhoneReg.onChange(e);
          }}
          onInput={(e) => {
            const el = e.target as HTMLInputElement;
            el.value = maskPhoneBR(el.value);
            financePhoneReg.onChange(
              e as unknown as React.ChangeEvent<HTMLInputElement>
            );
          }}
          errorMessage={errors.financeContact?.phone?.message as string}
        />
        <Input
          label="Email"
          placeholder="Digite o email"
          {...register("financeContact.email", {
            setValueAs: (v) =>
              typeof v === "string" && v.trim() === "" ? undefined : v,
          })}
          errorMessage={errors.financeContact?.email?.message as string}
        />
      </div>
    </div>
  );
}
