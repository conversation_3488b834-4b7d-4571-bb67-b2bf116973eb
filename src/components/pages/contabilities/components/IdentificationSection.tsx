import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { InlineSpinner } from "@/components/ui/inline-spinner";
import { formatCNPJ, maskCNPJ, unformatCNPJ } from "@/lib/format";
import { useFormContext } from "react-hook-form";
import { ContabilityValidationType } from "../validations";

type IdentificationSectionProps = {
  isEditMode?: boolean;
  isFetchingCompany: boolean;
};

export function IdentificationSection({
  isEditMode,
  isFetchingCompany,
}: IdentificationSectionProps) {
  const {
    formState: { errors },
    watch,
    setValue,
  } = useFormContext<ContabilityValidationType>();

  const cnpjMasked = maskCNPJ(watch("cnpj") || "");

  const formatAddress = () => {
    const address = watch("address");
    if (!address) return "Preenchido automaticamente";

    return `${watch("address.street")}, ${watch("address.number")}\n${
      watch("address.complement") || ""
    }\n${watch("address.neighborhood") || ""}\n${watch(
      "address.city"
    )}, ${watch("address.state")}\n${watch("address.zip")}`;
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5">
          Identificação
        </span>
      </div>

      <div className="flex flex-col gap-6">
        <Input
          label="CNPJ"
          placeholder="Digite o CNPJ"
          trailingIcon={isFetchingCompany ? <InlineSpinner /> : null}
          value={isEditMode ? formatCNPJ(watch("cnpj") || "") : cnpjMasked}
          onChange={
            isEditMode
              ? undefined
              : (e) => {
                  const v = e.target.value;
                  setValue("cnpj", unformatCNPJ(v));
                }
          }
          errorMessage={errors.cnpj?.message as string}
          disabled={!!isEditMode || isFetchingCompany}
          readOnly={!!isEditMode}
        />

        {isFetchingCompany ? (
          <Skeleton className="h-10 w-full" />
        ) : (
          <Input
            label="Nome fantasia"
            placeholder=""
            value={watch("alias") || "Preenchido automaticamente"}
            readOnly
            disabled
          />
        )}

        {isFetchingCompany ? (
          <Skeleton className="h-10 w-full" />
        ) : (
          <Input
            label="Razão Social"
            placeholder=""
            value={watch("name") || "Preenchido automaticamente"}
            readOnly
            disabled
          />
        )}

        {isFetchingCompany ? (
          <Skeleton className="h-24 w-full" />
        ) : (
          <Textarea
            label="Endereço"
            value={formatAddress()}
            readOnly
            disabled
          />
        )}
      </div>
    </div>
  );
}
