import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Controller, useFormContext } from "react-hook-form";
import { ContabilityValidationType } from "../validations";

export function StatusSection() {
  const { control } = useFormContext<ContabilityValidationType>();

  return (
    <div className="flex flex-col gap-6">
      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-2">
        Status
      </span>

      <div className="flex flex-col gap-6 mb-4">
        <Controller
          name="isActive"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value ? "active" : "inactive"}
              onValueChange={(v) => field.onChange(v === "active")}
            >
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="active" id="active" />
                  <Label htmlFor="active" className="cursor-pointer">
                    Ativo
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="inactive" id="inactive" />
                  <Label htmlFor="inactive" className="cursor-pointer">
                    Inativo
                  </Label>
                </div>
              </div>
            </RadioGroup>
          )}
        />
      </div>
    </div>
  );
}
