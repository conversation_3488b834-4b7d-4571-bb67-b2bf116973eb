import { Input } from "@/components/ui/input";
import { maskPhoneBR } from "@/lib/format";
import { useFormContext } from "react-hook-form";
import { ContabilityValidationType } from "../validations";

export function ContactSection() {
  const {
    register,
    formState: { errors },
  } = useFormContext<ContabilityValidationType>();

  const contactPhoneReg = register("contact.phone");

  return (
    <div className="flex flex-col gap-6">
      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-2">
        Contato Principal
      </span>

      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome"
          {...register("contact.name")}
          errorMessage={errors?.contact?.name?.message as string}
        />

        <Input
          label="Telefone"
          placeholder="Digite o telefone"
          name={contactPhoneReg.name}
          ref={contactPhoneReg.ref}
          onBlur={contactPhoneReg.onBlur}
          onChange={(e) => {
            e.target.value = maskPhoneBR(e.target.value);
            contactPhoneReg.onChange(e);
          }}
          onInput={(e) => {
            const el = e.target as HTMLInputElement;
            el.value = maskPhoneBR(el.value);
            contactPhoneReg.onChange(
              e as unknown as React.ChangeEvent<HTMLInputElement>
            );
          }}
          errorMessage={errors?.contact?.phone?.message as string}
        />

        <Input
          label="Email"
          placeholder="Digite o email"
          {...register("contact.email")}
          errorMessage={errors?.contact?.email?.message as string}
        />
      </div>
    </div>
  );
}
