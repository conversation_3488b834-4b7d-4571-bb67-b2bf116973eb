import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { useDebounce } from "@/hooks/use-debounce";
import { getCompanyByCNPJ } from "@/http/external";
import { getConsultancies, getConsultancyById } from "@/http/consultancies";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { ContabilityValidationType } from "../validations";

type UseContabilityDataProps = {
  form: UseFormReturn<ContabilityValidationType>;
  isEditMode?: boolean;
  consultancy?: {
    id: string;
    cnpj: string;
    alias: string;
    name: string;
    address?: {
      street?: string;
      number?: string;
      complement?: string;
      zip?: string;
      neighborhood?: string;
      city?: string;
      state?: string;
    };
    contact?: { name?: string; phone?: string; email?: string };
    isActive?: boolean;
  } | null;
  isOpen: boolean;
};

export function useContabilityData({
  form,
  isEditMode,
  consultancy,
  isOpen,
}: UseContabilityDataProps) {
  const { toast } = useToast();
  const debouncedCnpj = useDebounce(form.watch("cnpj") || "", 400);

  const { mutate: checkCnpjExists } = useMutation({
    mutationFn: (cnpj: string) => getConsultancies({ search: cnpj }),
    onSuccess: (data, cnpj) => {
      const consultancies = data?.consultancies || [];
      const exists = consultancies.some(
        (variable: { cnpj?: string }) =>
          String(variable.cnpj || "").replace(/\D/g, "") === cnpj
      );

      if (exists) {
        form.setError("cnpj", {
          type: "manual",
          message: "Já existe uma contabilidade cadastrada com este CNPJ",
        });
      } else {
        form.clearErrors("cnpj");
        fetchCompanyData(cnpj);
      }
    },
    onError: (error: AxiosError) => {
      console.error("Erro ao verificar CNPJ:", error);
      fetchCompanyData(debouncedCnpj);
    },
  });

  const { mutate: fetchCompanyData, isPending: isFetchingCompany } =
    useMutation({
      mutationFn: (cnpj: string) => getCompanyByCNPJ(cnpj),
      onSuccess: (company) => {
        form.setValue("alias", company.alias);
        form.setValue("name", company.name);
        form.setValue(
          "address",
          company.address as {
            street: string;
            number: string;
            complement?: string;
            zip: string;
            neighborhood: string;
            city: string;
            state: string;
          }
        );
      },
      onError: (error: AxiosError<{ message?: string }>) => {
        toast({
          title: "Erro ao buscar dados da empresa",
          description: error.response?.data?.message || "Erro desconhecido",
          variant: "destructive",
        });
      },
    });

  useEffect(() => {
    const validateAndFetch = async () => {
      if (isEditMode) return;
      if (!debouncedCnpj || debouncedCnpj.length !== 14) return;

      const isValidCnpjOnly = await form.trigger(["cnpj"]);
      if (!isValidCnpjOnly) return;

      checkCnpjExists(debouncedCnpj);
    };

    validateAndFetch();
  }, [debouncedCnpj, form, isEditMode, checkCnpjExists]);

  const { data: editData, isPending: isHydratingEdit } = useQuery({
    queryKey: ["consultancy", consultancy?.id],
    queryFn: () => {
      if (!consultancy?.id) throw new Error("No consultancy ID provided");
      return getConsultancyById({ id: consultancy.id });
    },
    enabled: isOpen && isEditMode && !!consultancy?.id,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (!editData || !isEditMode || !consultancy) return;

    const full = (editData as unknown as { data?: unknown }).data ?? editData;
    const entity = full as unknown as {
      cnpj?: string;
      alias?: string;
      name?: string;
      address?: {
        street?: string;
        number?: string;
        complement?: string;
        zip?: string;
        neighborhood?: string;
        city?: string;
        state?: string;
      };
      contact?: { name?: string; phone?: string; email?: string };
      isActive?: boolean;
    };

    form.reset({
      cnpj: entity.cnpj || consultancy.cnpj,
      alias: entity.alias || consultancy.alias,
      name: entity.name || consultancy.name,
      address: {
        street: entity.address?.street || "",
        number: entity.address?.number || "",
        complement: entity.address?.complement || "",
        zip: entity.address?.zip || "",
        neighborhood: entity.address?.neighborhood || "",
        city: entity.address?.city || "",
        state: entity.address?.state || "",
      },
      contact: {
        name: entity.contact?.name || consultancy.contact?.name || "",
        phone: entity.contact?.phone || consultancy.contact?.phone || "",
        email: entity.contact?.email || consultancy.contact?.email || "",
      },
      isActive: entity.isActive ?? consultancy.isActive ?? true,
    });
  }, [editData, isEditMode, consultancy, form]);

  return {
    isFetchingCompany,
    isHydratingEdit,
  };
}
