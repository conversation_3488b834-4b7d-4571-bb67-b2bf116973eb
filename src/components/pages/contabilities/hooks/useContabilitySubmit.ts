import { createConsultancy, updateConsultancy } from "@/http/consultancies";
import { unformatCNPJ } from "@/lib/format";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { UseFormReturn } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { AxiosError } from "axios";
import { ContabilityValidationType } from "../validations";

type UseContabilitySubmitProps = {
  form: UseFormReturn<ContabilityValidationType>;
  isEditMode?: boolean;
  consultancy?: {
    id: string;
    cnpj: string;
    alias: string;
    name: string;
    address?: {
      street?: string;
      number?: string;
      complement?: string;
      zip?: string;
      neighborhood?: string;
      city?: string;
      state?: string;
    };
    contact?: { name?: string; phone?: string; email?: string };
    isActive?: boolean;
  } | null;
  onSuccess: (title: string) => void;
  onClose: () => void;
  onShowSuccessModal: () => void;
};

export function useContabilitySubmit({
  form,
  isEditMode,
  consultancy,
  onSuccess,
  onClose,
  onShowSuccessModal,
}: UseContabilitySubmitProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { mutate: createMutation, isPending: isCreating } = useMutation({
    mutationFn: (data: ContabilityValidationType) => {
      const addressData = {
        street: data.address.street,
        number: data.address.number,
        complement: data.address.complement || undefined,
        zip: data.address.zip,
        neighborhood: data.address.neighborhood,
        city: data.address.city,
        state: data.address.state,
      };

      const contactData = {
        name: data.contact.name,
        phone: data.contact.phone,
        email: data.contact.email,
      };

      return createConsultancy({
        cnpj: unformatCNPJ(data.cnpj),
        alias: data.alias,
        name: data.name,
        address: addressData,
        contact: contactData,
        isActive: !!data.isActive,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["consultancies"] });
      onSuccess("Contabilidade cadastrada com sucesso!");
      onShowSuccessModal();
      onClose();
      form.reset({
        isActive: true,
        contact: { name: "", phone: "", email: "" },
      });
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      toast({
        title: "Erro ao cadastrar contabilidade",
        description: error.response?.data?.message,
        variant: "destructive",
      });
    },
  });

  const { mutate: updateMutation, isPending: isUpdating } = useMutation({
    mutationFn: (data: ContabilityValidationType) => {
      if (!consultancy?.id)
        throw new Error("ID da contabilidade não encontrado");

      const addressData = {
        street: data.address.street,
        number: data.address.number,
        complement: data.address.complement || undefined,
        zip: data.address.zip,
        neighborhood: data.address.neighborhood,
        city: data.address.city,
        state: data.address.state,
      };

      const contactData = {
        name: data.contact.name,
        phone: data.contact.phone,
        email: data.contact.email,
      };

      return updateConsultancy({
        id: consultancy.id,
        alias: data.alias,
        name: data.name,
        address: addressData,
        contact: contactData,
        isActive: !!data.isActive,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["consultancies"] });
      onSuccess("Contabilidade atualizada com sucesso!");
      onShowSuccessModal();
      onClose();
      form.reset({
        isActive: true,
        contact: { name: "", phone: "", email: "" },
      });
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      toast({
        title: "Erro ao atualizar contabilidade",
        description: error.response?.data?.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: ContabilityValidationType) => {
    const fieldsToValidate: (keyof ContabilityValidationType)[] = [
      "cnpj",
      "alias",
      "name",
      "address",
      "contact",
      "isActive",
    ];

    const valid = await form.trigger(fieldsToValidate);
    if (!valid) return;

    if (isEditMode && consultancy?.id) {
      updateMutation(data);
    } else {
      createMutation(data);
    }
  };

  return {
    handleSubmit,
    isLoading: isCreating || isUpdating,
  };
}
