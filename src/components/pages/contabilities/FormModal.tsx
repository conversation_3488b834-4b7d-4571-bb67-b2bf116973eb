"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/modals";
import { SuccessModal } from "@/components/modals/success-modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import {
  contabilityValidation,
  ContabilityValidationType,
} from "./validations";
import { useContabilityData } from "./hooks/useContabilityData";

import { IdentificationSection } from "./components/IdentificationSection";
import { ContactSection } from "./components/ContactSection";
import { StatusSection } from "./components/StatusSection";
import { useContabilitySubmit } from "./hooks/useContabilitySubmit";

type ContabilityModalFormProps = {
  triggerButton: React.ReactNode;
  isEditMode?: boolean;
  consultancy?: {
    id: string;
    cnpj: string;
    alias: string;
    name: string;
    address?: {
      street?: string;
      number?: string;
      complement?: string;
      zip?: string;
      neighborhood?: string;
      city?: string;
      state?: string;
    };
    contact?: { name?: string; phone?: string; email?: string };
    isActive?: boolean;
  } | null;
};

const getDefaultValues = (
  isEditMode?: boolean,
  consultancy?: ContabilityModalFormProps["consultancy"]
) => {
  if (isEditMode && consultancy) {
    return {
      cnpj: consultancy.cnpj,
      alias: consultancy.alias,
      name: consultancy.name,
      address: {
        street: consultancy.address?.street || "",
        number: consultancy.address?.number || "",
        complement: consultancy.address?.complement || "",
        zip: consultancy.address?.zip || "",
        neighborhood: consultancy.address?.neighborhood || "",
        city: consultancy.address?.city || "",
        state: consultancy.address?.state || "",
      } as {
        street: string;
        number: string;
        complement?: string;
        zip: string;
        neighborhood: string;
        city: string;
        state: string;
      },
      contact: {
        name: consultancy.contact?.name || "",
        phone: consultancy.contact?.phone || "",
        email: consultancy.contact?.email || "",
      },
      isActive: consultancy.isActive ?? true,
    };
  }

  return {
    isActive: true,
    contact: {
      name: "",
      phone: "",
      email: "",
    },
  };
};

export function ContabilityModalForm({
  triggerButton,
  isEditMode,
  consultancy,
}: ContabilityModalFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successTitle, setSuccessTitle] = useState("");

  const form = useForm<ContabilityValidationType>({
    resolver: zodResolver(contabilityValidation),
    defaultValues: getDefaultValues(isEditMode, consultancy),
  });

  const { isFetchingCompany } = useContabilityData({
    form,
    isEditMode,
    consultancy,
    isOpen,
  });

  const { handleSubmit, isLoading: isSubmitting } = useContabilitySubmit({
    form,
    isEditMode,
    consultancy,
    onSuccess: setSuccessTitle,
    onClose: () => setIsOpen(false),
    onShowSuccessModal: () => setShowSuccessModal(true),
  });

  const hasCompanyData = Boolean(
    form.watch("name") && form.watch("address.street") && form.watch("alias")
  );

  const handleModalClose = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      form.reset(getDefaultValues(isEditMode, consultancy));
    }
  };

  const isSubmitDisabled =
    isFetchingCompany ||
    isSubmitting ||
    !hasCompanyData ||
    !!form.formState.errors.cnpj ||
    (!isEditMode && (form.watch("cnpj") || "").replace(/\D/g, "").length < 14);

  return (
    <>
      <Modal
        title={isEditMode ? "Editar contabilidade" : "Nova contabilidade"}
        triggerButton={triggerButton}
        open={isOpen}
        onOpenChange={handleModalClose}
      >
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-8"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <IdentificationSection
              isEditMode={isEditMode}
              isFetchingCompany={isFetchingCompany}
            />

            <ContactSection />

            <StatusSection />

            <Button
              type="submit"
              variant="secondary"
              isLoading={isFetchingCompany || isSubmitting}
              disabled={isSubmitDisabled}
            >
              {isFetchingCompany
                ? "Buscando..."
                : isSubmitting
                ? isEditMode
                  ? "Salvando..."
                  : "Cadastrando..."
                : isEditMode
                ? "Salvar"
                : "Cadastrar"}
            </Button>
          </form>
        </FormProvider>
      </Modal>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={successTitle}
      />
    </>
  );
}
