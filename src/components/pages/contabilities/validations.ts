import { z } from "zod";
import { REQUIRED_MESSAGE, INVALID_EMAIL_MESSAGE } from "@/validations";

const addressSchema = z.object({
  street: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, { message: REQUIRED_MESSAGE }),
  number: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  complement: z.string().optional().nullable(),
  zip: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(8, { message: REQUIRED_MESSAGE }),
  neighborhood: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(2, { message: REQUIRED_MESSAGE }),
  city: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(2, { message: REQUIRED_MESSAGE }),
  state: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(2, { message: REQUIRED_MESSAGE })
    .max(2, { message: "Estado deve ter 2 caracteres" }),
});

const contactSchema = z.object({
  name: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, { message: REQUIRED_MESSAGE }),
  phone: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(10, { message: REQUIRED_MESSAGE }),
  email: z
    .string({ required_error: REQUIRED_MESSAGE })
    .email(INVALID_EMAIL_MESSAGE),
});

export const contabilityValidation = z.object({
  cnpj: z
    .string({ required_error: REQUIRED_MESSAGE })
    .regex(/^\d{14}$/, { message: "CNPJ deve ter 14 dígitos numéricos" }),
  alias: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  name: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: REQUIRED_MESSAGE }),
  address: addressSchema,
  contact: contactSchema,
  isActive: z.boolean().default(true),
});

export type ContabilityValidationType = z.infer<typeof contabilityValidation>;
