"use client";

import { Modal } from "@/components/modals";
import { SuccessModal } from "@/components/modals/success-modal";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MultiselectCombobox } from "@/components/ui/multiselect-combobox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  clearSpecialCaracters,
  convertToLabelAndValue,
  convertToStringArray,
  verifyIsAdmin,
} from "@/helpers/handleObjects";
import {
  useBusinessGroupsQuery,
  useBusinessUnitsQuery,
  useConsultanciesQuery,
  useUnionsQuery,
} from "@/hooks/useApi/useApi";
import { register as registerUser } from "@/http/auth";
import { IRegister } from "@/http/auth/types";
import { getUserById, updateUser } from "@/http/users";
import { maskPhoneBR, translateUserRole } from "@/lib/format";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import {
  UserEditUiValidation,
  UserValidation,
  UserValidationType,
} from "./validations";

type UserModalFormProps = {
  isEditMode?: boolean;
  triggerButton: React.ReactNode;
  user?: {
    id: string;
    name: string;
    email: string;
    phone: string;
    role: IRegister["role"];
    isActive: boolean;
    unions?: Array<{ id: string; name: string }>;
    businessGroups?: Array<{ id: string; name: string }>;
    businessUnits?: Array<{ id: string; name: string }>;
    consultancies?: Array<{ id: string; name: string }>;
  };
};

const roleOptions = [
  { label: translateUserRole("ADMIN_CLIENT"), value: "ADMIN_CLIENT" },
  { label: translateUserRole("ADMIN_SDG"), value: "ADMIN_SDG" },
  { label: translateUserRole("ATTENDANT_SDG"), value: "ATTENDANT_SDG" },
  { label: translateUserRole("ACCOUNTANT"), value: "ACCOUNTANT" },
  { label: translateUserRole("CONTROLLER_SDG"), value: "CONTROLLER_SDG" },
  { label: translateUserRole("FINANCIAL_CLIENT"), value: "FINANCIAL_CLIENT" },
  { label: translateUserRole("FINANCIAL_SDG"), value: "FINANCIAL_SDG" },
];

export function UserModalForm({
  triggerButton,
  isEditMode,
  user,
}: UserModalFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [statusValue, setStatusValue] = useState<"active" | "inactive">(
    user?.isActive ? "active" : "inactive"
  );
  const [showSuccess, setShowSuccess] = useState(false);
  const [successTitle, setSuccessTitle] = useState("");
  const [open, setOpen] = useState(false);

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<UserValidationType>({
    resolver: zodResolver(isEditMode ? UserEditUiValidation : UserValidation),
    defaultValues: user && {
      name: user.name,
      email: user.email,
      phoneNumber: maskPhoneBR(user.phone || ""),
      profile: { label: translateUserRole(user.role), value: user.role },
    },
  });

  const selectedUnions = watch("unions") || [];
  const selectedGroups = watch("groups") || [];
  const selectedRole = watch("profile")?.value as IRegister["role"];

  const { data: userDetails } = useQuery({
    queryKey: ["user", user?.id],
    queryFn: () => getUserById({ id: user?.id as string }),
    enabled: Boolean(open && isEditMode && user?.id),
    staleTime: 0,
  });

  useEffect(() => {
    if (!userDetails || !isEditMode || !open) return;
    reset({
      name: userDetails.user.name || "",
      email: userDetails.user.email || "",
      phoneNumber: userDetails.user.phone || "",
      profile: {
        value: userDetails.user.role,
        label: translateUserRole(userDetails.user.role),
      },
      accountants: convertToLabelAndValue(userDetails.user.consultancies || []),
      unions: convertToLabelAndValue(userDetails.user.unions || []),
      groups: convertToLabelAndValue(userDetails.user.businessGroups || []),
      units: convertToLabelAndValue(userDetails.user.businessUnits || []),
    });
  }, [userDetails, isEditMode, open, reset]);

  const { data: consultanciesData, isLoading: isLoadingConsultancies } =
    useConsultanciesQuery({
      filters: { page: 1, limit: 1000, isActive: true },
    });

  const { data: unionsData, isLoading: isLoadingUnions } = useUnionsQuery({
    filters: { page: 1, limit: 1000 },
  });

  const { data: groupsResp, isLoading: isLoadingGroups } =
    useBusinessGroupsQuery({
      filters: {
        page: 1,
        limit: 1000,
        unionIds: convertToStringArray(selectedUnions),
      },
    });

  const { data: unitsResp, isLoading: isLoadingUnits } = useBusinessUnitsQuery({
    filters: {
      page: 1,
      limit: 1000,
      businessGroupIds: convertToStringArray(selectedGroups),
    },
    enabled: selectedGroups.length > 0,
  });

  const { mutate: createUserMutation, isPending: isCreatingUser } = useMutation(
    {
      mutationFn: (data: UserValidationType) =>
        registerUser({
          email: data.email,
          name: data.name,
          role: data.profile?.value as IRegister["role"],
          phone: clearSpecialCaracters(data.phoneNumber || ""),
          unionIds: convertToStringArray(data.unions || []),
          businessGroupIds: convertToStringArray(data.groups || []),
          businessUnitIds: convertToStringArray(data.units || []),
          consultancyIds: convertToStringArray(data.accountants || []),
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["users"] });
        setOpen(false);
        setSuccessTitle("Usuário cadastrado com sucesso!");
        setShowSuccess(true);
        reset();
      },
      onError: (error: AxiosError<{ message?: string }>) => {
        toast({
          title: "Erro ao cadastrar usuário",
          description: error.response?.data?.message,
          variant: "destructive",
        });
      },
    }
  );

  const { mutate: updateUserMutation, isPending: isUpdatingUser } = useMutation(
    {
      mutationFn: (data: UserValidationType) =>
        updateUser({
          id: user?.id as string,
          name: data.name,
          email: data.email,
          phone: clearSpecialCaracters(data.phoneNumber || ""),
          role: data.profile?.value as IRegister["role"],
          isActive: statusValue === "active",
          unionIds: convertToStringArray(data.unions || []),
          businessGroupIds: convertToStringArray(data.groups || []),
          businessUnitIds: convertToStringArray(data.units || []),
          consultancyIds: convertToStringArray(data.accountants || []),
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["users"] });
        setOpen(false);
        setSuccessTitle("Usuário atualizado com sucesso!");
        setShowSuccess(true);
        reset();
      },
      onError: (error: AxiosError<{ message?: string }>) => {
        toast({
          title: "Erro ao atualizar usuário",
          description: error.response?.data?.message,
          variant: "destructive",
        });
      },
    }
  );

  const handleSubmitUser = (data: UserValidationType) => {
    if (isEditMode && user?.id) {
      updateUserMutation(data);
      return;
    }
    createUserMutation(data);
  };

  return (
    <>
      <Modal
        title={isEditMode ? "Editar usuário" : "Novo usuário"}
        triggerButton={triggerButton}
        open={open}
        onOpenChange={(newOpen) => {
          if (!newOpen) {
            reset();
          }
          setOpen(newOpen);
        }}
      >
        <form
          className="flex flex-col gap-16"
          onSubmit={handleSubmit(handleSubmitUser)}
        >
          <div className="space-y-6">
            <Input
              label="Nome"
              placeholder="Digite o nome."
              {...register("name")}
              errorMessage={errors.name?.message}
            />
            <Input
              label="Email"
              placeholder="Digite o e-maill"
              disabled={!!isEditMode}
              {...register("email")}
              errorMessage={errors.email?.message}
            />
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field }) => (
                <Input
                  label="Telefone"
                  placeholder="Digite o telefone"
                  value={maskPhoneBR(field.value || "")}
                  onChange={(e) => {
                    const rawValue = e.target.value.replace(/\D/g, "");
                    field.onChange(rawValue);
                  }}
                  errorMessage={errors.phoneNumber?.message}
                />
              )}
            />
            <Controller
              name="profile"
              control={control}
              render={({ field }) => (
                <Combobox
                  label="Perfil"
                  value={field.value}
                  onChange={field.onChange}
                  errorMessage={errors.profile?.message}
                  options={roleOptions}
                  placeholder="Selecione o perfil"
                />
              )}
            />

            {!verifyIsAdmin(selectedRole) && (
              <>
                <Controller
                  name="accountants"
                  control={control}
                  render={({ field }) => (
                    <MultiselectCombobox
                      label="Contabilidades"
                      value={field.value ?? []}
                      onChange={field.onChange}
                      errorMessage={errors.accountants?.message}
                      isLoading={isLoadingConsultancies}
                      options={convertToLabelAndValue(
                        consultanciesData?.consultancies || []
                      )}
                      placeholder="Selecione as contabilidades permitidas."
                    />
                  )}
                />

                <Controller
                  name="unions"
                  control={control}
                  render={({ field }) => (
                    <MultiselectCombobox
                      label="Sindicatos"
                      value={field.value ?? []}
                      onChange={(newValue) => {
                        field.onChange(newValue);
                        setValue("groups", []);
                        setValue("units", []);
                      }}
                      errorMessage={errors.unions?.message}
                      isLoading={isLoadingUnions}
                      options={convertToLabelAndValue(unionsData?.unions || [])}
                      placeholder="Selecione os sindicatos permitidos."
                    />
                  )}
                />

                <Controller
                  name="groups"
                  control={control}
                  render={({ field }) => (
                    <MultiselectCombobox
                      label="Grupos"
                      value={field.value ?? []}
                      onChange={(newValue) => {
                        field.onChange(newValue);
                        setValue("units", []);
                      }}
                      errorMessage={errors.groups?.message}
                      isLoading={isLoadingGroups}
                      options={convertToLabelAndValue(
                        groupsResp?.businessGroups || []
                      )}
                      placeholder="Selecione os grupos permitidos."
                      disabled={selectedUnions.length === 0}
                    />
                  )}
                />

                <Controller
                  name="units"
                  control={control}
                  render={({ field }) => (
                    <MultiselectCombobox
                      label="Unidades"
                      value={field.value ?? []}
                      onChange={field.onChange}
                      errorMessage={errors.units?.message}
                      isLoading={isEditMode ? false : isLoadingUnits}
                      options={convertToLabelAndValue(
                        unitsResp?.businessUnits || []
                      )}
                      placeholder="Selecione as unidades permitidas."
                      disabled={
                        isEditMode ? false : selectedGroups.length === 0
                      }
                    />
                  )}
                />
              </>
            )}

            {isEditMode && (
              <RadioGroup
                value={statusValue}
                onValueChange={(v) =>
                  setStatusValue(v as "active" | "inactive")
                }
              >
                <Label>Status</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="active" id="active" />
                    <Label htmlFor="active" className="cursor-pointer">
                      Ativo
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="inactive" id="inactive" />
                    <Label htmlFor="inactive" className="cursor-pointer">
                      Inativo
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            )}
          </div>
          <Button
            type="submit"
            variant="secondary"
            className="w-full"
            isLoading={isCreatingUser || isUpdatingUser}
            disabled={isCreatingUser || isUpdatingUser}
          >
            {isEditMode ? "Salvar" : "Cadastrar"}
          </Button>
        </form>
      </Modal>
      <SuccessModal
        isOpen={showSuccess}
        onClose={() => setShowSuccess(false)}
        title={
          successTitle ||
          (isEditMode
            ? "Usuário atualizado com sucesso!"
            : "Usuário cadastrado com sucesso!")
        }
      />
    </>
  );
}
