"use client";
import { UsersTable } from "@/components/tables/users";
import { Input } from "@/components/ui/input";
import { Plus, Search, UserCog } from "lucide-react";
import { UserModalForm } from "./userModal";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { useDebounce } from "@/hooks/use-debounce";

export function UsersPage() {
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 400);

  return (
    <div className="w-full">
      <header className="w-full h-16 bg-[#256caf16] text-primary font-bold flex items-center justify-between p-6">
        <div className="flex gap-1">
          <UserCog size={20} absoluteStrokeWidth />
          <span>Usuários</span>
        </div>

        <div className="flex gap-4 items-center">
          <Input
            trailingIcon={<Search size={16} />}
            placeholder="Pesquise pelo nome"
            className="bg-white text-black font-thin h-10 w-64 rounded-sm"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />

          <UserModalForm
            triggerButton={
              <Button variant="secondary" className="h-10 px-8">
                <Plus size={20} />
                Novo usuário
              </Button>
            }
          />
        </div>
      </header>

      <UsersTable search={debouncedSearch} />
    </div>
  );
}
