import { z } from "zod";

export const invoicingBatchValidation = z
  .object({
    selectedUnionIds: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .min(1, "Selecione pelo menos 1 sindicato"),
    selectedBusinessGroupIds: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .min(1, "Selecione pelo menos 1 grupo empresarial"),
    selectedBusinessUnitIds: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .min(1, "Selecione pelo menos 1 unidade"),
    baseDueDayStart: z
      .number()
      .int()
      .min(1, "Dia inicial deve ser entre 1 e 31")
      .max(31, "Dia inicial deve ser entre 1 e 31"),
    baseDueDayEnd: z
      .number()
      .int()
      .min(1, "Dia final deve ser entre 1 e 31")
      .max(31, "Dia final deve ser entre 1 e 31"),
    invoiceDueDate: z.date().optional(),
    maintainDueDate: z.boolean().default(true),
  })
  .refine((data) => data.baseDueDayStart <= data.baseDueDayEnd, {
    message: "Dia inicial não pode ser maior que o dia final",
    path: ["baseDueDayStart"],
  });

export type InvoicingBatchValidationType = z.infer<
  typeof invoicingBatchValidation
>;
