"use client";

import { Modal } from "@/components/modals";
import { SuccessModal } from "@/components/modals/success-modal";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MultiselectCombobox } from "@/components/ui/multiselect-combobox";
import {
  useBusinessGroupsQuery,
  useBusinessUnitsQuery,
  useUnionsQuery,
} from "@/hooks/useApi/useApi";
import { createBillingBatch } from "@/http/billing";
import type { CreateBillingBatchRequest } from "@/http/billing/types";
import type { Union } from "@/http/unions/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { useRouter } from "next/navigation";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import {
  invoicingBatchValidation,
  type InvoicingBatchValidationType,
} from "./validations";
import { DatePicker } from "@/components/ui/datepicker";

type UserModalFormProps = {
  triggerButton: React.ReactNode;
};

export function InvoicingModalForm({ triggerButton }: UserModalFormProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [successOpen, setSuccessOpen] = React.useState(false);
  const { toast } = useToast();

  const { handleSubmit, control, setValue, watch, formState, clearErrors } =
    useForm<InvoicingBatchValidationType>({
      resolver: zodResolver(invoicingBatchValidation),
      defaultValues: {
        selectedUnionIds: [],
        selectedBusinessGroupIds: [],
        selectedBusinessUnitIds: [],
        baseDueDayStart: 1,
        baseDueDayEnd: 31,
        invoiceDueDate: undefined,
        maintainDueDate: true,
      },
    });

  const { errors } = formState;
  const selectedUnionIds = watch("selectedUnionIds");
  const selectedBusinessGroupIds = watch("selectedBusinessGroupIds");
  const maintainDueDate = watch("maintainDueDate");

  const { data: unionsData } = useUnionsQuery({
    filters: { page: 1, limit: 50 },
  });

  const { data: businessGroupsData, isLoading: isLoadingBusinessGroups } =
    useBusinessGroupsQuery({
      filters: { unionIds: selectedUnionIds.map((u) => u.value) },
      enabled: selectedUnionIds.length > 0,
    });

  const { data: businessUnitsData } = useBusinessUnitsQuery({
    filters: { businessGroupIds: selectedBusinessGroupIds.map((g) => g.value) },
    enabled: selectedBusinessGroupIds.length > 0,
  });

  const { mutate: createBillingBatchMutation, isPending: isCreatingBatch } =
    useMutation({
      mutationFn: (values: InvoicingBatchValidationType) => {
        const payload: CreateBillingBatchRequest = {
          ...values,
          selectedUnionIds: values.selectedUnionIds.map((u) => u.value),
          selectedBusinessGroupIds: values.selectedBusinessGroupIds.map(
            (g) => g.value
          ),
          selectedBusinessUnitIds: values.selectedBusinessUnitIds.map(
            (u) => u.value
          ),
          invoiceDueDate: maintainDueDate
            ? ""
            : (values.invoiceDueDate?.toISOString() as string),
        };
        return createBillingBatch(payload);
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["billing-batches"] });
        setIsFormOpen(false);
        setSuccessOpen(true);
      },
      onError: (error: AxiosError<{ message?: string }>) => {
        console.log(error);
        toast({
          title: "Erro ao gerar faturamento",
          description: error.response?.data?.message || "Erro desconhecido",
          variant: "destructive",
        });
      },
    });

  const handleSubmitInvoicing = (values: InvoicingBatchValidationType) => {
    createBillingBatchMutation(values);
  };

  const resetForm = () => {
    setValue("selectedUnionIds", []);
    setValue("selectedBusinessGroupIds", []);
    setValue("selectedBusinessUnitIds", []);
    setValue("baseDueDayStart", 1);
    setValue("baseDueDayEnd", 31);
    setValue("invoiceDueDate", undefined);
    setValue("maintainDueDate", true);
  };

  const businessUnitPlans = businessUnitsData?.businessUnits.map((unit) => {
    return {
      label: unit.alias,
      value: unit.id,
    };
  });

  return (
    <>
      <Modal
        title="Novo Faturamento"
        triggerButton={triggerButton}
        open={isFormOpen}
        onOpenChange={() => {
          setIsFormOpen((state) => !state);
          resetForm();
          clearErrors();
        }}
      >
        <form
          className="flex flex-col gap-4"
          onSubmit={handleSubmit(handleSubmitInvoicing)}
        >
          <div className="flex flex-col gap-6">
            <Controller
              name="selectedUnionIds"
              control={control}
              render={({ field }) => (
                <MultiselectCombobox
                  label="Sindicatos"
                  placeholder="Selecione os sindicatos"
                  options={
                    unionsData?.unions.map((u: Union) => ({
                      label: u.alias || u.name,
                      value: u.id,
                    })) || []
                  }
                  value={field.value}
                  onChange={field.onChange}
                  errorMessage={errors.selectedUnionIds?.message as string}
                />
              )}
            />
            <Controller
              name="selectedBusinessGroupIds"
              control={control}
              render={({ field }) => (
                <MultiselectCombobox
                  label="Grupos empresariais"
                  placeholder={
                    selectedUnionIds?.length
                      ? "Selecione os grupos empresariais"
                      : "Selecione primeiro os sindicatos"
                  }
                  options={
                    (businessGroupsData &&
                      businessGroupsData?.businessGroups?.map(
                        (g: { id: string; alias?: string; name?: string }) => ({
                          label: g.alias || g.name || "",
                          value: g.id,
                        })
                      )) ||
                    []
                  }
                  value={field.value}
                  onChange={field.onChange}
                  disabled={
                    !selectedUnionIds?.length || isLoadingBusinessGroups
                  }
                  errorMessage={
                    errors.selectedBusinessGroupIds?.message as string
                  }
                />
              )}
            />
            <Controller
              name="selectedBusinessUnitIds"
              control={control}
              render={({ field }) => (
                <MultiselectCombobox
                  label="Unidades"
                  placeholder={
                    selectedBusinessGroupIds?.length
                      ? "Selecione as unidades"
                      : "Selecione primeiro os grupos"
                  }
                  options={businessUnitPlans || []}
                  value={field.value}
                  onChange={field.onChange}
                  disabled={!selectedBusinessGroupIds?.length}
                  errorMessage={
                    errors.selectedBusinessUnitIds?.message as string
                  }
                />
              )}
            />
          </div>

          <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-2">
            Dia do vencimento base
          </span>

          <div className="flex flex-col gap-6 w-full mb-8">
            <div className="flex gap-4 w-full">
              <Controller
                name="baseDueDayStart"
                control={control}
                render={({ field }) => (
                  <Input
                    type="number"
                    min={1}
                    max={31}
                    label="Dia inicial"
                    placeholder="Ex.: 5"
                    value={field.value?.toString() || ""}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                    errorMessage={errors.baseDueDayStart?.message as string}
                  />
                )}
              />
              <Controller
                name="baseDueDayEnd"
                control={control}
                render={({ field }) => (
                  <Input
                    type="number"
                    min={1}
                    max={31}
                    label="Dia final"
                    placeholder="Ex.: 10"
                    value={field.value?.toString() || ""}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                    errorMessage={errors.baseDueDayEnd?.message as string}
                  />
                )}
              />
            </div>
            <Controller
              name="invoiceDueDate"
              control={control}
              render={({ field }) => (
                <DatePicker
                  modal
                  label="Data de vencimento das faturas"
                  placeholder="dd/mm/aaaa"
                  value={field.value}
                  onChange={field.onChange}
                  disabled={maintainDueDate}
                  errorMessage={errors.invoiceDueDate?.message as string}
                  startMonth={
                    new Date(
                      new Date().setFullYear(new Date().getFullYear() - 1)
                    )
                  }
                  endMonth={
                    new Date(
                      new Date().setFullYear(new Date().getFullYear() + 1)
                    )
                  }
                />
              )}
            />

            <div className="flex items-center gap-2">
              <Controller
                name="maintainDueDate"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="maintainDueDate"
                    className="h-5 w-5"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                )}
              />
              <Label
                className="text-sm text-gray-700 cursor-pointer"
                htmlFor="maintainDueDate"
              >
                Manter o vencimento do cadastro do cliente
              </Label>
            </div>
          </div>

          <Button
            type="submit"
            variant="secondary"
            isLoading={isCreatingBatch}
            disabled={isCreatingBatch}
          >
            {isCreatingBatch ? "Gerando..." : "Gerar faturas"}
          </Button>
        </form>
      </Modal>
      <SuccessModal
        isOpen={successOpen}
        onClose={() => {
          setSuccessOpen(false);
          resetForm();
          router.push("/faturamento");
        }}
        title="As faturas estão sendo geradas"
        message={"Avisaremos você por e-mail quando\no processo for concluído!"}
      />
    </>
  );
}
