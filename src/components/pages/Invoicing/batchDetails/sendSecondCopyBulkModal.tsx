"use client";

import React from "react";
import { Modal } from "@/components/modals";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";

type SendSecondCopyBulkModalProps = {
  trigger: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onConfirm?: () => void;
};

export function SendSecondCopyBulkModal({
  trigger,
  open,
  onOpenChange,
  onConfirm,
}: SendSecondCopyBulkModalProps) {
  return (
    <Modal
      title="Enviar segunda via"
      triggerButton={trigger}
      headerAlign="center"
      open={open}
      onOpenChange={onOpenChange}
    >
      <div className="flex flex-col gap-12">
        <p className="text-base leading-snug text-muted-foreground text-center max-w-sm mx-auto">
          Tem certeza que deseja enviar a segunda via da(s) parcela(s)
          selecionada(s)?
        </p>

        <div className="flex items-center justify-between gap-4 pt-2">
          <DialogClose asChild>
            <Button
              variant="outline"
              className="flex-1 font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
            >
              Cancelar
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              className="flex-1 font-bold"
              variant="secondary"
              onClick={() => onConfirm?.()}
            >
              Sim, enviar
            </Button>
          </DialogClose>
        </div>
      </div>
    </Modal>
  );
}
