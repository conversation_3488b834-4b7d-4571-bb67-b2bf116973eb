"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Barcode,
  Download,
  ListFilter,
  Pencil,
  Plus,
  CheckCircle2,
  Receipt,
} from "lucide-react";
import { InvoicingModalForm } from "../formModal";
import { DataTable } from "@/components/ui/datatable";
import type { Column } from "@/types/table";
import type { InvoiceRow } from "./mocks";
import { StatusBadge } from "@/components/ui/status-badge";
import { Checkbox } from "@/components/ui/checkbox";
import { FilterInvoicesModal } from "./filter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye } from "lucide-react";
import { ViewInvoiceModal } from "./viewInvoiceModal";
import { EditDueDateModal } from "./editDueDateModal";
import { EditDueDateBulkModal } from "./editDueDateBulkModal";
import { RegisterPaymentBulkModal } from "./registerPaymentBulkModal";
import { SendSecondCopyBulkModal } from "./sendSecondCopyBulkModal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type PageProps = {
  batchId: string;
};

export function InvoicingBatchDetailsPage({ batchId }: PageProps) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [rows, setRows] = useState<InvoiceRow[]>([]);
  const [batchHeader, setBatchHeader] = useState<{
    sequenceNumber: number;
    generatedAt?: string;
    status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
    processedInvoices?: number;
    failedInvoices?: number;
    totalAmount?: number;
    totalExpectedInvoices?: number;
  } | null>(null);

  const [viewInvoice, setViewInvoice] = useState<InvoiceRow | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [editInvoice, setEditInvoice] = useState<InvoiceRow | null>(null);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isBulkEditOpen, setIsBulkEditOpen] = useState(false);
  const [isRegisterPaymentOpen, setIsRegisterPaymentOpen] = useState(false);
  const [isSendSecondCopyOpen, setIsSendSecondCopyOpen] = useState(false);
  type ActiveFilters = {
    amountStart?: string;
    amountEnd?: string;
    dueDateStart?: string;
    dueDateEnd?: string;
    genDateStart?: string;
    genDateEnd?: string;
    paymentStatuses?: string[];
    generationStatuses?: string[];
  };
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({});

  const formatCurrency = (value: number) => {
    try {
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
      }).format(value || 0);
    } catch {
      return `R$ ${Number(value || 0).toFixed(2)}`;
    }
  };

  const formatDate = (value?: string) => {
    if (!value) return "";
    const datePart = value.slice(0, 10); // YYYY-MM-DD
    if (/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
      const [y, m, d] = datePart.split("-");
      return `${d}/${m}/${y}`;
    }
    try {
      const d = new Date(value);
      if (Number.isNaN(d.getTime())) return value;
      const dd = String(d.getUTCDate()).padStart(2, "0");
      const mm = String(d.getUTCMonth() + 1).padStart(2, "0");
      const yyyy = d.getUTCFullYear();
      return `${dd}/${mm}/${yyyy}`;
    } catch {
      return value;
    }
  };

  const fetchInvoices = async () => {
    try {
      setIsLoading(true);
      const { getBatchInvoices } = await import("@/http/billing");

      const params: {
        batchId: string;
        page: number;
        limit: number;
        minAmount?: number;
        maxAmount?: number;
        dueDateFrom?: string;
        dueDateTo?: string;
        generatedDateFrom?: string;
        generatedDateTo?: string;
        statuses?: Array<"PENDING" | "PAID" | "OVERDUE" | "CANCELLED">;
      } = {
        batchId,
        page: currentPage,
        limit: pageSize,
      };

      if (activeFilters.amountStart)
        params.minAmount = Number(activeFilters.amountStart);
      if (activeFilters.amountEnd)
        params.maxAmount = Number(activeFilters.amountEnd);
      if (activeFilters.dueDateStart)
        params.dueDateFrom = activeFilters.dueDateStart;
      if (activeFilters.dueDateEnd) params.dueDateTo = activeFilters.dueDateEnd;
      if (activeFilters.genDateStart)
        params.generatedDateFrom = activeFilters.genDateStart;
      if (activeFilters.genDateEnd)
        params.generatedDateTo = activeFilters.genDateEnd;
      if (activeFilters.paymentStatuses?.length) {
        type PaymentLabel = "PENDENTE" | "PAGO" | "VENCIDO";
        const statusMap: Record<PaymentLabel, "PENDING" | "PAID" | "OVERDUE"> =
          {
            PENDENTE: "PENDING",
            PAGO: "PAID",
            VENCIDO: "OVERDUE",
          };
        const mapped = (activeFilters.paymentStatuses as PaymentLabel[])
          .map((s) => statusMap[s])
          .filter(Boolean) as Array<"PENDING" | "PAID" | "OVERDUE">;
        if (mapped.length) params.statuses = mapped;
      }

      const res = await getBatchInvoices(params);

      type ApiInvoice = {
        id: string | number;
        unionName?: string;
        businessGroupName?: string;
        businessUnitName?: string;
        employeeCount?: number | string;
        amount?: number | string;
        dueDate?: string;
        generatedAt?: string;
        status?: "PENDING" | "PAID" | "OVERDUE" | "CANCELLED";
        generationStatus?: "SUCCESS" | "ERROR" | string;
      };

      const invoices: ApiInvoice[] =
        (res.invoices as unknown as ApiInvoice[]) || [];

      const pickLabel = (
        value: unknown,
        fallbackKeys: string[] = []
      ): string => {
        if (typeof value === "string") return value;
        if (value && typeof value === "object") {
          for (const key of fallbackKeys) {
            const maybe = (value as Record<string, unknown>)[key];
            if (typeof maybe === "string" && maybe) return maybe;
          }
        }
        return "";
      };

      const mapped: InvoiceRow[] = invoices.map((inv) => {
        const anyInv = inv as unknown as Record<string, unknown>;
        const unionStr =
          (anyInv["unionName"] as string) ??
          (anyInv["unionAlias"] as string) ??
          pickLabel(anyInv["union"], ["alias", "name"]) ??
          (anyInv["union"] as string) ??
          "";

        const groupStr =
          (anyInv["businessGroupName"] as string) ??
          (anyInv["businessGroupAlias"] as string) ??
          pickLabel(anyInv["businessGroup"], ["alias", "name"]) ??
          (anyInv["groupName"] as string) ??
          (anyInv["groupAlias"] as string) ??
          (anyInv["group"] as string) ??
          "";

        const unitStr =
          (anyInv["businessUnitName"] as string) ??
          (anyInv["businessUnitAlias"] as string) ??
          pickLabel(anyInv["businessUnit"], ["alias", "name"]) ??
          (anyInv["unitName"] as string) ??
          (anyInv["unitAlias"] as string) ??
          (anyInv["unit"] as string) ??
          "";

        const genRaw = String(
          (anyInv["generationStatus"] as string | undefined) ??
            inv.generationStatus ??
            ""
        );
        const genUpper = genRaw.toUpperCase();

        return {
          id: String(inv.id),
          union: String(unionStr || ""),
          group: String(groupStr || ""),
          unit: String(unitStr || ""),
          lives: Number(
            (anyInv["employeeCount"] as number | string | undefined) ?? 0
          ),
          amount: formatCurrency(Number(inv.amount ?? 0)),
          dueDate: formatDate(inv.dueDate),
          generatedAt: formatDate(inv.generatedAt),
          paymentStatus:
            inv.status === "PAID"
              ? "Pago"
              : inv.status === "OVERDUE"
              ? "Atrasado"
              : inv.status === "CANCELLED"
              ? "Cancelado"
              : "Pendente",
          generationStatus:
            genUpper === "ERROR"
              ? "Erro"
              : genUpper === "SUCCESS"
              ? "Sucesso"
              : "Pendente",
        };
      });

      setRows(mapped);
      setTotalPages(res.totalPages || 1);
    } catch {
      setRows([]);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInvoices();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchId, currentPage, pageSize, JSON.stringify(activeFilters)]);

  const fetchBatchHeader = async () => {
    try {
      const { getBillingBatchDetails } = await import("@/http/billing");
      const res = await getBillingBatchDetails(batchId);
      const b = res.batch;
      setBatchHeader({
        sequenceNumber: b.sequenceNumber,
        generatedAt: b.generatedAt,
        status: b.status,
        processedInvoices: b.processedInvoices,
        failedInvoices: b.failedInvoices,
        totalAmount: b.totalAmount,
        totalExpectedInvoices: b.totalExpectedInvoices,
      });
    } catch {
      // ignore header error to not block table
    }
  };

  useEffect(() => {
    fetchBatchHeader();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchId]);

  useEffect(() => {
    // SSE de progresso (fallback: ignora se falhar)
    let es: EventSource | null = null;
    try {
      const open = async () => {
        const { api } = await import("@/services/api");
        const base = api.defaults.baseURL || "";
        const token =
          typeof window !== "undefined"
            ? localStorage.getItem("@gestao-sindicatos:access-token")
            : null;
        const url = `${base}/api/billing/batches/${batchId}/progress${
          token ? `?access_token=${token}` : ""
        }`;
        es = new EventSource(url);
        es.onmessage = (ev) => {
          try {
            const payload = JSON.parse(ev.data);
            if (payload?.status && payload?.processedInvoices !== undefined) {
              setBatchHeader((prev) =>
                prev
                  ? {
                      ...prev,
                      status: payload.status,
                      processedInvoices: payload.processedInvoices,
                      totalExpectedInvoices:
                        payload.totalExpectedInvoices ??
                        prev.totalExpectedInvoices,
                    }
                  : prev
              );
            }
          } catch {}
        };
      };
      open();
    } catch {
      // ignore
    }
    return () => {
      if (es) es.close();
    };
  }, [batchId]);

  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const allSelected =
    selectedIds.length > 0 && selectedIds.length === rows.length;
  const toggleAll = (checked: boolean) => {
    setSelectedIds(checked ? rows.map((r) => r.id) : []);
  };
  const toggleOne = (id: string, checked: boolean) => {
    setSelectedIds((prev) =>
      checked ? [...new Set([...prev, id])] : prev.filter((x) => x !== id)
    );
  };

  const columns: Column<InvoiceRow>[] = [
    {
      header: (
        <Checkbox
          className="h-5 w-5"
          checked={allSelected}
          onCheckedChange={(v) => toggleAll(Boolean(v))}
        />
      ),
      render: (r) => (
        <Checkbox
          className="h-5 w-5"
          checked={selectedIds.includes(r.id)}
          onCheckedChange={(v) => toggleOne(r.id, Boolean(v))}
        />
      ),
    },
    { header: "Sindicato", accessor: "union" },
    { header: "Grupo", accessor: "group" },
    { header: "Unidade", accessor: "unit" },
    {
      header: "Vidas",
      render: (r) => (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="cursor-help underline decoration-dotted">
                {String(r.lives)}
              </span>
            </TooltipTrigger>
            <TooltipContent
              side="bottom"
              className="overflow-visible bg-black text-white rounded-xl text-sm px-4 py-3 relative data-[side=bottom]:after:content-[''] data-[side=bottom]:after:absolute data-[side=bottom]:after:-top-2 data-[side=bottom]:after:left-1/2 data-[side=bottom]:after:-translate-x-1/2 data-[side=bottom]:after:translate-y-1 data-[side=bottom]:after:w-4 data-[side=bottom]:after:h-4 data-[side=bottom]:after:bg-black data-[side=bottom]:after:rotate-45 data-[side=bottom]:after:rounded-[3px]"
            >
              <div className="flex flex-col gap-1">
                <span>
                  Plano familiar: <strong>8</strong>
                </span>
                <span>
                  Plano individual: <strong>16</strong>
                </span>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
    },
    { header: "Valor", accessor: "amount" },
    { header: "Vencimento", accessor: "dueDate" },
    { header: "Geração", accessor: "generatedAt" },
    {
      header: "Status do Pagamento",
      render: (r) => (
        <StatusBadge
          label={r.paymentStatus}
          variant={
            r.paymentStatus === "Pago"
              ? "success"
              : r.paymentStatus === "Atrasado"
              ? "error"
              : r.paymentStatus === "Cancelado"
              ? "neutral"
              : "warning"
          }
        />
      ),
    },
    {
      header: "Status da Geração",
      render: (r) => (
        <StatusBadge
          label={r.generationStatus}
          variant={
            r.generationStatus === "Sucesso"
              ? "success"
              : r.generationStatus === "Erro"
              ? "error"
              : "neutral"
          }
        />
      ),
    },
    {
      header: (
        <FilterInvoicesModal
          triggerButton={
            <Button variant="ghost" className="px-2 h-9 border border-gray-200">
              <ListFilter />
            </Button>
          }
          onApply={(filters: ActiveFilters) => {
            setCurrentPage(1);
            setActiveFilters(filters || {});
          }}
        />
      ),
      isActionColumn: true,
      render: (row) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-9 h-9 rounded-md flex items-center justify-center text-gray-500 hover:bg-primary hover:text-white">
              <MoreHorizontal size={18} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-72">
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault();
                setViewInvoice(row);
                setIsViewOpen(true);
              }}
            >
              <Eye className="mr-2 h-4 w-4 text-primary" /> Visualizar
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault();
                setEditInvoice(row);
                setIsEditOpen(true);
              }}
            >
              <Pencil className="mr-2 h-4 w-4 text-primary" /> Editar vencimento
            </DropdownMenuItem>
            <DropdownMenuItem>
              <CheckCircle2 className="mr-2 h-4 w-4 text-primary" /> Atualizar
              status do pagamento
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Download className="mr-2 h-4 w-4 text-primary" /> Exportar
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Barcode className="mr-2 h-4 w-4 text-primary" /> Enviar 2ª via
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="w-full">
      <header className="w-full h-16 bg-[#256caf16] text-primary font-bold flex items-center justify-between p-6">
        <div className="flex gap-1">
          <Receipt size={20} absoluteStrokeWidth />
          <span>Faturamento</span>
        </div>

        <InvoicingModalForm
          triggerButton={
            <Button variant="secondary" className="h-10 px-8">
              <Plus size={20} />
              Novo faturamento
            </Button>
          }
        />
      </header>

      <div className="p-6 flex flex-col min-h-[calc(100vh-8rem)]">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-6">
            <Button
              variant="outline"
              onClick={() => router.push("/faturamento")}
              className="h-9 gap-2 text-[#256CAF] border-[#256CAF] hover:bg-[#256caf16] hover:text-[#256CAF]"
            >
              <ArrowLeft className="h-4 w-4" /> Voltar para todos os lotes
            </Button>
            <span className="text-sm text-primary font-medium">
              Exibindo faturas do lote{" "}
              {String(batchHeader?.sequenceNumber ?? "...").padStart(4, "0")}
            </span>
          </div>

          <div className="flex items-center gap-3">
            <EditDueDateBulkModal
              trigger={
                <Button
                  variant="bulk"
                  className="h-9 px-3"
                  disabled={selectedIds.length === 0}
                >
                  <Pencil className="h-4 w-4 mr-2" /> Editar vencimento
                </Button>
              }
              open={isBulkEditOpen}
              onOpenChange={setIsBulkEditOpen}
              onSave={async (newDate) => {
                if (!newDate) return;
                const { changeInvoicesDueDate } = await import(
                  "@/http/billing"
                );
                await changeInvoicesDueDate({
                  invoiceIds: selectedIds,
                  newDueDate: newDate,
                });
                setSelectedIds([]);
                fetchInvoices();
              }}
            />
            <RegisterPaymentBulkModal
              trigger={
                <Button
                  variant="bulk"
                  className="h-9 px-3"
                  disabled={selectedIds.length === 0}
                >
                  <CheckCircle2 className="h-4 w-4 mr-2" /> Atualizar status do
                  pagamento
                </Button>
              }
              open={isRegisterPaymentOpen}
              onOpenChange={setIsRegisterPaymentOpen}
              onSave={async (statusLabel) => {
                const { updateInvoicesStatus } = await import("@/http/billing");
                const map: Record<
                  string,
                  "PENDING" | "PAID" | "OVERDUE" | "CANCELLED"
                > = {
                  Pendente: "PENDING",
                  Pago: "PAID",
                  Vencido: "OVERDUE",
                } as const;
                const status = map[statusLabel] || "PENDING";
                await updateInvoicesStatus({ invoiceIds: selectedIds, status });
                setSelectedIds([]);
                fetchInvoices();
              }}
            />
            <Button
              variant="bulk"
              className="h-9 px-3"
              disabled={selectedIds.length === 0}
            >
              <Download className="h-4 w-4 mr-2" /> Exportar
            </Button>
            <SendSecondCopyBulkModal
              trigger={
                <Button
                  variant="bulk"
                  className="h-9 px-3"
                  disabled={selectedIds.length === 0}
                >
                  <Barcode className="h-4 w-4 mr-2" /> Enviar 2ª via
                </Button>
              }
              open={isSendSecondCopyOpen}
              onOpenChange={setIsSendSecondCopyOpen}
              onConfirm={async () => {
                const { sendInvoicesSecondCopy } = await import(
                  "@/http/billing"
                );
                await sendInvoicesSecondCopy({ invoiceIds: selectedIds });
                setSelectedIds([]);
              }}
            />
          </div>
        </div>

        <DataTable
          columns={columns}
          data={rows}
          keyExtractor={(r) => r.id}
          className="w-full p-0 flex-1"
          isLoading={isLoading}
          firstColumnClassName="w-[64px] pl-6 pr-2"
          pagination={{
            currentPage,
            totalPages,
            onPageChange: setCurrentPage,
            pageSize,
            onPageSizeChange: setPageSize,
          }}
          openFilterModal={
            <FilterInvoicesModal
              triggerButton={
                <Button
                  variant="ghost"
                  className="px-2 h-9 border border-gray-200"
                >
                  <ListFilter />
                </Button>
              }
              onApply={(filters: ActiveFilters) => {
                setCurrentPage(1);
                setActiveFilters(filters || {});
              }}
            />
          }
        />

        {viewInvoice && (
          <ViewInvoiceModal
            trigger={<span />}
            data={{
              union: viewInvoice.union,
              group: viewInvoice.group,
              unit: viewInvoice.unit,
              lives: viewInvoice.lives,
              amount: viewInvoice.amount,
              dueDate: viewInvoice.dueDate,
              generatedAt: viewInvoice.generatedAt,
              paymentStatus: viewInvoice.paymentStatus as
                | "Pendente"
                | "Pago"
                | "Vencido",
              generationStatus: viewInvoice.generationStatus as
                | "Sucesso"
                | "Erro",
            }}
            open={isViewOpen}
            onOpenChange={setIsViewOpen}
          />
        )}
        {editInvoice && (
          <EditDueDateModal
            trigger={<span />}
            data={{
              union: editInvoice.union,
              group: editInvoice.group,
              unit: editInvoice.unit,
              lives: editInvoice.lives,
              amount: editInvoice.amount,
              dueDate: editInvoice.dueDate,
              generatedAt: editInvoice.generatedAt,
              paymentStatus: editInvoice.paymentStatus as
                | "Pendente"
                | "Pago"
                | "Vencido",
              generationStatus: editInvoice.generationStatus as
                | "Sucesso"
                | "Erro",
            }}
            open={isEditOpen}
            onOpenChange={setIsEditOpen}
          />
        )}
      </div>
    </div>
  );
}
