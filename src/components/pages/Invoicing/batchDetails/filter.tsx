/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React from "react";
import { Modal } from "@/components/modals";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { CheckboxList } from "@/components/ui/filterCheckbox";

type FilterState = {
  amountStart?: string;
  amountEnd?: string;
  dueDateStart?: string;
  dueDateEnd?: string;
  genDateStart?: string;
  genDateEnd?: string;
  paymentStatuses?: string[];
  generationStatuses?: string[];
  unions?: string[];
  groups?: string[];
  units?: string[];
};

export function FilterInvoicesModal({
  triggerButton,
  onApply,
}: {
  triggerButton: React.ReactNode;
  onApply?: (filters: FilterState) => void;
}) {
  const [filters, setFilters] = React.useState<FilterState>({
    paymentStatuses: [],
    generationStatuses: [],
    unions: [],
    groups: [],
    units: [],
  });

  const clearSection = (keys: (keyof FilterState)[]) => {
    setFilters((prev) => {
      const next = { ...prev } as any;
      keys.forEach((k) => {
        next[k] = Array.isArray(prev[k]) ? [] : undefined;
      });
      return next;
    });
  };

  const clearAll = () => {
    setFilters({
      paymentStatuses: [],
      generationStatuses: [],
      unions: [],
      groups: [],
      units: [],
    });
  };

  const SectionHeader = ({
    title,
    onClear,
  }: {
    title: string;
    onClear?: () => void;
  }) => (
    <div className="flex items-center justify-between">
      <span className="font-bold text-primary">{title}</span>
      <button
        className="text-xs text-muted-foreground/60 hover:underline"
        onClick={onClear}
      >
        Limpar filtro
      </button>
    </div>
  );

  return (
    <Modal title="Filtrar por" triggerButton={triggerButton} headerAlign="left">
      <div className="flex flex-col gap-6">
        {/* Valor da fatura */}
        <div className="flex flex-col gap-4">
          <SectionHeader
            title="Valor da fatura"
            onClear={() => clearSection(["amountStart", "amountEnd"])}
          />
          <div className="grid grid-cols-2 gap-4">
            <Input placeholder="Ex.: R$0,00" label="Valor inicial" />
            <Input placeholder="Ex.: R$1.000,00" label="Valor final" />
          </div>
        </div>

        <Separator />

        {/* Vencimento da fatura */}
        <div className="flex flex-col gap-4">
          <SectionHeader
            title="Vencimento da fatura"
            onClear={() => clearSection(["dueDateStart", "dueDateEnd"])}
          />
          <div className="grid grid-cols-2 gap-4">
            <Input type="date" placeholder="dd/mm/aaaa" label="Data inicial" />
            <Input type="date" placeholder="dd/mm/aaaa" label="Data final" />
          </div>
        </div>

        <Separator />

        {/* Geração da fatura */}
        <div className="flex flex-col gap-4">
          <SectionHeader
            title="Geração da fatura"
            onClear={() => clearSection(["genDateStart", "genDateEnd"])}
          />
          <div className="grid grid-cols-2 gap-4">
            <Input type="date" placeholder="dd/mm/aaaa" label="Data inicial" />
            <Input type="date" placeholder="dd/mm/aaaa" label="Data final" />
          </div>
        </div>

        <Separator />

        {/* Status do pagamento */}
        <CheckboxList
          title="Status do pagamento"
          options={[
            { value: "PENDENTE", label: "Pendente" },
            { value: "PAGO", label: "Pago" },
            { value: "VENCIDO", label: "Vencido" },
          ]}
          selectedValues={filters.paymentStatuses || []}
          onSelectionChange={(vals) =>
            setFilters((p) => ({ ...p, paymentStatuses: vals }))
          }
          hideSearchBar
        />

        <Separator />

        {/* Status da geração */}
        <CheckboxList
          title="Status da geração"
          options={[
            { value: "SUCESSO", label: "Sucesso" },
            { value: "ERRO", label: "Erro" },
          ]}
          selectedValues={filters.generationStatuses || []}
          onSelectionChange={(vals) =>
            setFilters((p) => ({ ...p, generationStatuses: vals }))
          }
          hideSearchBar
        />

        <Separator />

        {/* Sindicatos / Grupos / Unidades */}
        <div className="flex flex-col gap-6">
          <CheckboxList
            title="Sindicatos"
            options={[1, 2, 3, 4].map((i) => ({
              label: `Sindicato ${i}`,
              value: `u-${i}`,
            }))}
            selectedValues={filters.unions || []}
            onSelectionChange={(vals) =>
              setFilters((p) => ({ ...p, unions: vals }))
            }
            searchPlaceholder="Digite o nome do sindicato"
          />

          <Separator />

          <CheckboxList
            title="Grupos"
            options={[1, 2, 3, 4].map((i) => ({
              label: `Grupo ${i}`,
              value: `g-${i}`,
            }))}
            selectedValues={filters.groups || []}
            onSelectionChange={(vals) =>
              setFilters((p) => ({ ...p, groups: vals }))
            }
            searchPlaceholder="Digite o nome do grupo"
          />

          <Separator />

          <CheckboxList
            title="Unidades"
            options={[1, 2, 3, 4].map((i) => ({
              label: `Unidade ${i}`,
              value: `bu-${i}`,
            }))}
            selectedValues={filters.units || []}
            onSelectionChange={(vals) =>
              setFilters((p) => ({ ...p, units: vals }))
            }
            searchPlaceholder="Digite o nome da unidade"
          />
        </div>

        <div className="flex w-full gap-3 pt-2">
          <Button
            variant="outline"
            className="flex-1 font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
            onClick={clearAll}
          >
            Limpar todos
          </Button>
          <Button
            variant="secondary"
            className="flex-1 font-bold"
            onClick={() => onApply?.(filters)}
          >
            Aplicar
          </Button>
        </div>
      </div>
    </Modal>
  );
}
