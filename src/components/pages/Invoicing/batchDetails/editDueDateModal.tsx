"use client";

import { Modal } from "@/components/modals";
import { DialogClose } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { StatusBadge } from "@/components/ui/status-badge";
import { Button } from "@/components/ui/button";

type EditDueDateModalProps = {
  trigger: React.ReactNode;
  data: {
    union: string;
    group: string;
    unit: string;
    lives: number;
    amount: string;
    dueDate: string;
    generatedAt: string;
    paymentStatus: "Pendente" | "Pago" | "Vencido";
    generationStatus: "Sucesso" | "Erro";
  };
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};

export function EditDueDateModal({
  trigger,
  data,
  open,
  onOpenChange,
}: EditDueDateModalProps) {
  return (
    <Modal
      title="Editar data de vencimento da fatura"
      triggerButton={trigger}
      headerAlign="center"
      open={open}
      onOpenChange={onOpenChange}
    >
      <div className="flex flex-col gap-6">
        <Input label="Sindicato" value={data.union} disabled />
        <Input label="Grupo empresarial" value={data.group} disabled />
        <Input label="Unidades" value={data.unit} disabled />
        <Input label="Vidas" value={String(data.lives)} disabled />
        <Input label="Valor da fatura" value={data.amount} disabled />
        <Input
          label="Data de vencimento da fatura"
          type="date"
          placeholder="dd/mm/aaaa"
          defaultValue={data.dueDate}
        />
        <Input
          label="Data de geração da fatura"
          value={data.generatedAt}
          disabled
        />

        <div className="flex flex-col gap-1">
          <span className="text-sm">Status do Pagamento</span>
          <StatusBadge
            label={data.paymentStatus}
            variant={
              data.paymentStatus === "Pago"
                ? "success"
                : data.paymentStatus === "Vencido"
                ? "error"
                : "warning"
            }
            className="py-3"
          />
        </div>

        <div className="flex flex-col gap-1">
          <span className="text-sm">Status da Geração</span>
          <StatusBadge
            label={data.generationStatus}
            variant={data.generationStatus === "Sucesso" ? "success" : "error"}
            className="py-3"
          />
        </div>

        <div className="pt-2">
          <DialogClose asChild>
            <Button className="w-full" variant="secondary">
              Fechar
            </Button>
          </DialogClose>
        </div>
      </div>
    </Modal>
  );
}
