"use client";

import React from "react";
import { Modal } from "@/components/modals";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";

type EditDueDateBulkModalProps = {
  trigger: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSave?: (newDueDate: string) => void;
};

export function EditDueDateBulkModal({
  trigger,
  open,
  onOpenChange,
  onSave,
}: EditDueDateBulkModalProps) {
  const [date, setDate] = React.useState("");

  return (
    <Modal
      title="Editar data de vencimento das faturas selecionadas"
      triggerButton={trigger}
      headerAlign="center"
      open={open}
      onOpenChange={(o) => {
        if (!o) setDate("");
        onOpenChange?.(o);
      }}
    >
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-3">
          <Input
            type="date"
            placeholder="dd/mm/aaaa"
            label="Data de vencimento das faturas"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>

        <div className="pt-2">
          <DialogClose asChild>
            <Button
              className="w-full"
              variant="secondary"
              onClick={() => onSave?.(date)}
              disabled={!date}
            >
              Salvar alterações
            </Button>
          </DialogClose>
        </div>
      </div>
    </Modal>
  );
}
