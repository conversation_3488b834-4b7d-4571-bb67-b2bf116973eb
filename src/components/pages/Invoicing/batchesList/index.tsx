"use client";

import { InlineSpinner } from "@/components/ui/inline-spinner";
import { ItemsLength } from "@/components/ui/items-length";
import { Pagination } from "@/components/ui/pagination-component";
import { useBillingBatchesQuery } from "@/hooks/useApi/useApi";
import { useEffect, useState } from "react";
import { BatchCard } from "./BatchCard";

export function BatchesList() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  const { data: batchesData, isLoading: isLoadingBatchesQuery } =
    useBillingBatchesQuery({
      filters: {
        page,
        limit: pageSize,
      },
    });

  const [totalPages, setTotalPages] = useState(
    batchesData?.pagination.totalPages || 1
  );

  console.log(batchesData);

  useEffect(() => {
    setTotalPages(batchesData?.pagination.totalPages || 1);
  }, [batchesData?.pagination.totalPages]);

  return (
    <div className="flex flex-col min-h-[calc(100vh-8rem)] w-full p-6 gap-4">
      {isLoadingBatchesQuery ? (
        <div className="w-full flex items-center justify-center p-12">
          <InlineSpinner size="lg" ariaLabel="Carregando lotes" />
        </div>
      ) : batchesData?.data.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-semibold text-gray-700">
              Ainda não existe nenhuma fatura gerada
            </div>
            <div className="text-gray-500 mt-2">
              Clique no botão “Novo faturamento” para criar o primeiro registro
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {batchesData?.data.map((batchItem) => (
            <BatchCard
              key={batchItem.id}
              batch={{
                ...batchItem,
                progress: batchItem.progress ?? 0,
                totals: {
                  total:
                    batchItem.totalInvoices ?? batchItem.processedInvoices ?? 0,
                  error:
                    batchItem.totalFailedInvoices ??
                    batchItem.failedInvoices ??
                    0,
                  success:
                    batchItem.totalSuccessfulInvoices ??
                    Math.max(
                      0,
                      (batchItem.processedInvoices ?? 0) -
                        (batchItem.failedInvoices ?? 0)
                    ),
                },
              }}
            />
          ))}
        </div>
      )}

      {batchesData?.data && batchesData?.data.length > 0 && (
        <div className="flex w-full items-center justify-center mt-4">
          <ItemsLength
            totalItems={batchesData.pagination.total}
            itemsPerPage={pageSize}
            onChange={setPageSize}
          />
          <Pagination page={page} totalPages={totalPages} onChange={setPage} />
        </div>
      )}
    </div>
  );
}
