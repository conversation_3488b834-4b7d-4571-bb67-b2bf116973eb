"use client";

import { useRouter } from "next/navigation";

import { Progress } from "@/components/ui/progress";
import { useBatchProgressSSE } from "@/hooks/use-batch-progress-sse";
import { useIsInViewport } from "@/hooks/use-is-in-viewport";
import { formatBRL } from "@/lib/format";
import { InvoicingBatch } from "@/types/batch";
import { RenderStatusLabel, RenderStatusPill } from "./status";

type BatchCardProps = {
  batch: InvoicingBatch;
};

export function BatchCard({ batch }: BatchCardProps) {
  const router = useRouter();
  const { ref, isInViewport } = useIsInViewport<HTMLDivElement>({
    root: null,
    rootMargin: "100px",
    threshold: 0.1,
  });
  const shouldTrack =
    (batch.status === "PROCESSING" || batch.status === "PENDING") &&
    isInViewport;

  const { progress: liveProgress, status: liveStatus } = useBatchProgressSSE(
    batch.id,
    shouldTrack
  );

  const effectiveProgress = Math.min(
    100,
    Math.max(0, Number(liveProgress ?? batch.progress ?? 0))
  );
  const effectiveStatus = (liveStatus ||
    batch.status) as InvoicingBatch["status"];
  const percentageFormatted = `${effectiveProgress}%`;

  const generatedAtText = batch.generatedAt
    ? new Date(batch.generatedAt).toLocaleString("pt-BR")
    : "—";

  return (
    <div
      ref={ref}
      role="button"
      tabIndex={0}
      onClick={() => router.push(`/faturamento/lotes/${batch.id}`)}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ")
          router.push(`/faturamento/lotes/${batch.id}`);
      }}
      className="cursor-pointer text-left bg-white rounded-md border p-4 shadow-sm hover:shadow transition-shadow space-y-2"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-2">
          <span className="font-semibold text-gray-900">
            Lote {String(batch.sequenceNumber).padStart(4, "0")}
          </span>
          {RenderStatusLabel(effectiveStatus)}
        </div>
      </div>

      {batch.totalAmount && (
        <div>
          <span className="text-sm text-gray-700">Valor total</span>
          <div className="text-lg font-semibold text-gray-900">
            {formatBRL(batch.totalAmount)}
          </div>
        </div>
      )}

      <RenderStatusPill batch={batch} shouldTrack={shouldTrack} />

      {(effectiveStatus === "PROCESSING" || effectiveStatus === "PENDING") && (
        <div className="space-y-2">
          <span className="text-sm text-gray-700">Progresso</span>
          <div className="flex gap-2 items-center">
            <Progress value={effectiveProgress} />
            <span className="text-xs text-gray-500">{percentageFormatted}</span>
          </div>
        </div>
      )}

      <div className="pt-4 border-t text-xs text-gray-500 space-y-1">
        <div>Gerado em: {generatedAtText}</div>
        {batch.requestedBy && <div>Solicitado por: {batch.requestedBy}</div>}
      </div>
    </div>
  );
}
