import { useBatchProgressSSE } from "@/hooks/use-batch-progress-sse";
import { InvoicingBatch } from "@/types/batch";

type RenderStatusPillProps = {
  batch: InvoicingBatch;
  shouldTrack: boolean;
};

export function RenderStatusLabel(status: InvoicingBatch["status"]) {
  if (status === "PENDING")
    return (
      <span className="text-xs bg-gray-100 text-gray-600 rounded-sm px-2 py-1">
        Pendente
      </span>
    );
  if (status === "PROCESSING")
    return (
      <span className="text-xs bg-blue-100 text-blue-700 rounded-sm px-2 py-1">
        Em processamento
      </span>
    );
  if (status === "COMPLETED")
    return (
      <span className="text-xs bg-emerald-100 text-emerald-700 rounded-sm px-2 py-1">
        Concluído
      </span>
    );
  return (
    <span className="text-xs bg-red-100 text-red-700 rounded-sm px-2 py-1">
      Falhou
    </span>
  );
}

export function RenderStatusPill({
  batch,
  shouldTrack,
}: RenderStatusPillProps) {
  const { processedInvoices, totalExpectedInvoices, failedInvoices } =
    useBatchProgressSSE(batch.id, shouldTrack);

  return (
    <div className="grid grid-cols-2 xl:grid-cols-3 gap-4">
      <div className="flex items-center gap-2 ">
        <span className="text-sm text-gray-500 w-[60px]">Sucesso</span>
        <span className="text-sm bg-emerald-100 text-emerald-700 rounded-sm px-2 py-1">
          {typeof processedInvoices === "number" &&
          typeof failedInvoices === "number"
            ? Math.max(0, processedInvoices - failedInvoices)
            : batch.totals.success || 0}
        </span>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-500 w-[60px]">Erro</span>
        <span className="text-sm bg-red-100 text-red-700 rounded-sm px-2 py-1">
          {typeof failedInvoices === "number"
            ? failedInvoices
            : batch.totals.error || 0}
        </span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-500 w-[60px]">Total</span>
        <span className="text-sm bg-gray-100 text-gray-700 rounded-sm px-2 py-1">
          {typeof totalExpectedInvoices === "number"
            ? totalExpectedInvoices
            : batch.totals.total || 0}
        </span>
      </div>
    </div>
  );
}
