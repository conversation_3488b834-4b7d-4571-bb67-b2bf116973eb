import { Input } from "@/components/ui/input";
import { useFormContext } from "react-hook-form";

export function SecondStep() {
  const {
    register,
    formState: { errors },
    watch,
    setValue,
  } = useFormContext();

  const plans =
    (watch("plans") as Array<{ planId: string; employeeCount: number }>) || [];

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-6">
        <Input
          type="number"
          label="Dia de vencimento da fatura"
          placeholder="Digite o dia de vencimento da fatura"
          {...register("dueDay", { valueAsNumber: true })}
          errorMessage={errors?.dueDay?.message as string}
        />
      </div>
      <div className="flex flex-col gap-4">
        <span className="text-sm">Quantidade de funcionários por plano</span>
        {plans.map((plan, index) => {
          return (
            <div
              key={plan.planId || String(index)}
              className="flex items-center justify-between gap-8"
            >
              <span className="flex-1 p-2 bg-gray-900/10 rounded-sm text-sm h-11 flex items-center">
                {(
                  watch("unionPlans") as
                    | Array<{ id: string; name: string }>
                    | undefined
                )?.find((p) => p.id === plan.planId)?.name || "Plano"}
              </span>
              <div className="flex items-center gap-2 w-56">
                <Input
                  type="number"
                  placeholder="Ex.: 0"
                  value={plan.employeeCount}
                  onChange={(e) => {
                    const v = Number(e.target.value || 0);
                    const next = [...plans];
                    next[index] = { ...plan, employeeCount: v };
                    setValue(
                      "plans",
                      next as unknown as Array<{
                        planId: string;
                        employeeCount: number;
                      }>,
                      { shouldValidate: true }
                    );
                  }}
                  errorMessage={
                    (
                      errors?.plans as unknown as Array<{
                        employeeCount?: { message?: string };
                      }>
                    )?.[index]?.employeeCount?.message as string
                  }
                />
              </div>
            </div>
          );
        })}
        {plans.length === 0 && (
          <span className="text-xs text-gray-500">
            Nenhum plano encontrado para o sindicato selecionado.
          </span>
        )}
      </div>
    </div>
  );
}
