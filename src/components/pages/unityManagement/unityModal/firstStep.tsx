/* eslint-disable react-hooks/exhaustive-deps */
import { Combobox } from "@/components/ui/combobox";
import { InlineSpinner } from "@/components/ui/inline-spinner";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { useDebounce } from "@/hooks/use-debounce";
import { useToast } from "@/hooks/use-toast";
import { getBusinessGroups } from "@/http/business-groups";
import { getBusinessUnits } from "@/http/business-units";
import { getCompanyByCNPJ } from "@/http/external";
import { maskCEP, maskCNPJ, unformatCNPJ } from "@/lib/format";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useMemo, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";

export function FirstStep({ isEditMode }: { isEditMode?: boolean }) {
  const {
    register,
    control,
    formState: { errors },
    watch,
    setError,
    clearErrors,
    setValue,
  } = useFormContext();
  const { toast } = useToast();

  const { data, isFetching } = useQuery({
    queryKey: ["business-groups"],
    queryFn: () => getBusinessGroups({ page: 1, limit: 999 }),
    refetchOnWindowFocus: false,
  });

  // CNPJ: valida unidade existente e busca dados externos
  const [cnpjDisplay, setCnpjDisplay] = useState("");
  const [isFetchingCompany, setIsFetchingCompany] = useState(false);
  useEffect(() => {
    const current = watch("cnpj") as string | undefined;
    if (current && !cnpjDisplay) {
      setCnpjDisplay(maskCNPJ(current));
    }
  }, []);

  // Mantém o input preenchido em modo de edição, inclusive após hidratação por ID
  const cnpjFormValue = watch("cnpj") as string | undefined;
  useEffect(() => {
    if (isEditMode) {
      setCnpjDisplay(maskCNPJ(cnpjFormValue || ""));
    }
  }, [isEditMode, cnpjFormValue]);
  const cnpjDigits = (watch("cnpj") || "").replace(/\D/g, "");
  const debouncedCnpj = useDebounce(cnpjDigits, 400);
  useEffect(() => {
    if (isEditMode) return;
    let cancelled = false;
    (async () => {
      if (debouncedCnpj.length !== 14) return;
      try {
        setIsFetchingCompany(true);
        const res = await getBusinessUnits({ search: debouncedCnpj });
        const payload =
          (
            res as unknown as {
              data?: { businessUnits?: Array<{ cnpj?: string }> };
            }
          ).data ??
          (res as unknown as { businessUnits?: Array<{ cnpj?: string }> });
        const exists = (
          (payload?.businessUnits || []) as Array<{ cnpj?: string }>
        ).some((u) => (u.cnpj || "").replace(/\D/g, "") === debouncedCnpj);
        if (exists) {
          setError("cnpj", {
            type: "manual",
            message: "Já existe uma unidade com este CNPJ",
          });
          return;
        }
        clearErrors("cnpj");

        const company = await getCompanyByCNPJ(debouncedCnpj);
        if (cancelled) return;
        setValue("socialReason", company.name);
        setValue("fantasyName", company.alias);
        setValue("cnae", company.cnae);
        setValue("addressObj", {
          street: company.address.street,
          number: company.address.number,
          complement: company.address.complement,
          zip: company.address.zip,
          neighborhood: company.address.neighborhood,
          city: company.address.city,
          state: company.address.state,
        });
      } catch (error: unknown) {
        const err = error as { response?: { data?: { message?: string } } };
        toast({
          title: "Erro",
          description:
            err?.response?.data?.message || "Erro ao buscar dados da empresa",
          variant: "destructive",
        });
      } finally {
        setIsFetchingCompany(false);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [debouncedCnpj, setError, clearErrors, setValue, toast]);

  const addressText = useMemo(() => {
    const addr = (watch("addressObj") || {}) as {
      street?: string;
      number?: string;
      complement?: string;
      zip?: string;
      neighborhood?: string;
      city?: string;
      state?: string;
    };
    const parts: string[] = [];
    if (addr.street && addr.number)
      parts.push(`${addr.street}, ${addr.number}`);
    if (addr.complement) parts.push(addr.complement);
    if (addr.neighborhood) parts.push(addr.neighborhood);
    const cityState = [addr.city, addr.state].filter(Boolean).join(", ");
    if (cityState) parts.push(cityState);
    if (addr.zip) parts.push(maskCEP(addr.zip));
    return parts.join("\n");
  }, [watch("addressObj")]);

  return (
    <div className="flex flex-col gap-8">
      <Controller
        name="businessGroup"
        control={control}
        render={({ field }) => (
          <Combobox
            label="Grupo empresarial vinculado"
            placeholder="Selecione o grupo empresarial"
            options={
              data?.businessGroups?.map(
                (business: { id: string; alias?: string; name?: string }) => ({
                  label: business.alias || business.name || "",
                  value: business.id,
                })
              ) || []
            }
            onChange={field.onChange}
            value={field.value}
            errorMessage={errors?.businessGroup?.message as string}
            disabled={!!isEditMode || isFetching}
          />
        )}
      />
      <Input
        label="CNPJ"
        placeholder="Digite o CNPJ"
        trailingIcon={isFetchingCompany ? <InlineSpinner /> : null}
        value={cnpjDisplay}
        onChange={(e) => {
          const masked = maskCNPJ(e.target.value);
          setCnpjDisplay(masked);
          setValue("cnpj", unformatCNPJ(masked));
        }}
        errorMessage={errors.cnpj?.message as string}
        disabled={!!isEditMode || isFetchingCompany}
        readOnly={!!isEditMode}
      />
      {isFetchingCompany ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="Razão Social"
          placeholder="Preenchido automaticamente"
          {...register("socialReason")}
          errorMessage={errors.socialReason?.message as string}
          disabled
        />
      )}
      {isFetchingCompany ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="Nome fantasia"
          placeholder="Preenchido automaticamente"
          {...register("fantasyName")}
          errorMessage={errors.fantasyName?.message as string}
          disabled
        />
      )}
      {isFetchingCompany ? (
        <Skeleton className="h-24 w-full" />
      ) : (
        <Textarea
          label="Endereço"
          value={addressText || "Preenchido automaticamente"}
          readOnly
          disabled
        />
      )}
      {isFetchingCompany ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="CNAE"
          placeholder="Preenchido automaticamente"
          {...register("cnae")}
          errorMessage={errors.cnae?.message as string}
          disabled
        />
      )}
    </div>
  );
}
