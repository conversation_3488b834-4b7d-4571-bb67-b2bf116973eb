"use client";

import { Modal } from "@/components/modals";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getBusinessGroups } from "@/http/business-groups";
import { useDebounce } from "@/hooks/use-debounce";
import { Button } from "@/components/ui/button";
import { CheckboxList } from "@/components/ui/filterCheckbox";

type FilterModalProps = {
  triggerButton: React.ReactNode;
  defaultGroupIds?: string[];
  onApply: (filters: {
    groupIds: string[];
    dueStart?: number;
    dueEnd?: number;
    empStart?: number;
    empEnd?: number;
  }) => void;
};

type ApiGroup = { id?: string; alias?: string; name?: string };

export function UnityFilterModal({
  triggerButton,
  defaultGroupIds = [],
  onApply,
}: FilterModalProps) {
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedGroups, setSelectedGroups] =
    useState<string[]>(defaultGroupIds);
  useEffect(() => {
    setSelectedGroups(defaultGroupIds);
  }, [defaultGroupIds]);
  const [groupSearch, setGroupSearch] = useState("");
  const debouncedSearch = useDebounce(groupSearch, 300);
  const [page, setPage] = useState(1);
  const [items, setItems] = useState<ApiGroup[]>([]);
  // kept for potential future container-specific refs

  const { data, isFetching, refetch } = useQuery<{
    businessGroups?: ApiGroup[];
    totalPages?: number;
  }>({
    queryKey: ["business-groups", debouncedSearch, page],
    queryFn: async () => {
      const res = await getBusinessGroups({
        page,
        limit: 10,
        search: debouncedSearch || undefined,
      });
      const payload =
        (
          res as unknown as {
            data?: { businessGroups?: ApiGroup[]; totalPages?: number };
          }
        ).data ??
        (res as unknown as {
          businessGroups?: ApiGroup[];
          totalPages?: number;
        });
      return payload;
    },
    refetchOnWindowFocus: false,
    enabled: isOpen,
  });

  useEffect(() => {
    // quando muda a page, anexamos; quando a busca é vazia e page===1, garantimos reset
    if (!data) return;
    const list = data.businessGroups || [];
    setItems((prev) => {
      const base = page === 1 ? [] : prev;
      const seen = new Set(base.map((g) => g.id));
      return [...base, ...list.filter((g) => !seen.has(String(g.id)))];
    });
  }, [data, page]);

  useEffect(() => {
    // quando a busca muda, resetamos para a primeira página e limpamos itens
    setItems([]);
    setPage(1);
    if (debouncedSearch.trim() === "" && isOpen) {
      queryClient.removeQueries({ queryKey: ["business-groups"] });
      queryClient.invalidateQueries({ queryKey: ["business-groups"] });
      refetch();
    }
  }, [debouncedSearch, isOpen, queryClient, refetch]);

  const hasMore = ((data?.totalPages ?? 1) as number) > page;

  // selection handled by CheckboxList onSelectionChange
  const [dueStart, setDueStart] = useState<string>("");
  const [dueEnd, setDueEnd] = useState<string>("");
  const [empStart, setEmpStart] = useState<string>("");
  const [empEnd, setEmpEnd] = useState<string>("");

  return (
    <Modal
      title="Filtrar por"
      triggerButton={triggerButton}
      contentClassName="w-96 rounded-lg p-6 gap-12"
      open={isOpen}
      onOpenChange={setIsOpen}
      headerAlign="left"
    >
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between mb-2">
          <span className="font-bold text-primary">Grupos</span>
          <button
            className="text-xs text-muted-foreground/60 hover:underline"
            onClick={() => {
              setSelectedGroups([]);
              setGroupSearch("");
              setItems([]);
              setPage(1);
              queryClient.removeQueries({ queryKey: ["business-groups"] });
              queryClient.invalidateQueries({ queryKey: ["business-groups"] });
              refetch();
            }}
          >
            Limpar filtro
          </button>
        </div>
        <CheckboxList
          title="Grupos"
          options={(items || []).map((g) => ({
            label: (g.alias || g.name || "") as string,
            value: String(g.id || ""),
          }))}
          selectedValues={selectedGroups}
          onSelectionChange={setSelectedGroups}
          searchPlaceholder="Digite o nome do grupo"
          searchValue={groupSearch}
          onSearchChange={setGroupSearch}
          isLoading={!!isFetching && page === 1}
          onLoadMore={() => {
            if (hasMore && !isFetching) setPage((p) => p + 1);
          }}
          hasMore={hasMore}
          isLoadingMore={!!isFetching && page > 1}
          emptyMessage="Nenhum grupo encontrado"
          hideHeader
        />

        <Separator />

        <div className="flex items-center justify-between mb-2">
          <span className="font-bold text-primary">Vencimento da fatura</span>
          <button
            className="text-xs text-muted-foreground/60 hover:underline"
            onClick={() => {
              setDueStart("");
              setDueEnd("");
            }}
          >
            Limpar filtro
          </button>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Label className="text-sm">Valor inicial</Label>
            <Input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="Ex.: 1"
              value={dueStart}
              onChange={(e) => setDueStart(e.target.value.replace(/\D/g, ""))}
            />
          </div>
          <div className="flex flex-col gap-1">
            <Label className="text-sm">Valor final</Label>
            <Input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="Ex.: 15"
              value={dueEnd}
              onChange={(e) => setDueEnd(e.target.value.replace(/\D/g, ""))}
            />
          </div>
        </div>

        <Separator />

        <div className="flex items-center justify-between mb-2">
          <span className="font-bold text-primary">
            Quantidade de funcionários
          </span>
          <button
            className="text-xs text-muted-foreground/60 hover:underline"
            onClick={() => {
              setEmpStart("");
              setEmpEnd("");
            }}
          >
            Limpar filtro
          </button>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Label className="text-sm">Valor inicial</Label>
            <Input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="Ex.: 500"
              value={empStart}
              onChange={(e) => setEmpStart(e.target.value.replace(/\D/g, ""))}
            />
          </div>
          <div className="flex flex-col gap-1">
            <Label className="text-sm">Valor final</Label>
            <Input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="Ex.: 1000"
              value={empEnd}
              onChange={(e) => setEmpEnd(e.target.value.replace(/\D/g, ""))}
            />
          </div>
        </div>

        <div className="flex w-full gap-3 pt-2">
          <Button
            variant="outline"
            className="flex-1 font-bold"
            onClick={() => {
              setSelectedGroups([]);
              setGroupSearch("");
              setDueStart("");
              setDueEnd("");
              setEmpStart("");
              setEmpEnd("");
              setItems([]);
              setPage(1);
              queryClient.removeQueries({ queryKey: ["business-groups"] });
              queryClient.invalidateQueries({ queryKey: ["business-groups"] });
              refetch();
            }}
          >
            Limpar todos
          </Button>
          <Button
            variant="secondary"
            className="flex-1 font-bold"
            onClick={() => {
              onApply({
                groupIds: selectedGroups,
                dueStart: dueStart ? Number(dueStart) : undefined,
                dueEnd: dueEnd ? Number(dueEnd) : undefined,
                empStart: empStart ? Number(empStart) : undefined,
                empEnd: empEnd ? Number(empEnd) : undefined,
              });
              setIsOpen(false);
            }}
          >
            Aplicar
          </Button>
        </div>
      </div>
    </Modal>
  );
}
