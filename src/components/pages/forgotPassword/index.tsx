"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { ForgotPasswordFormValues, forgotPasswordSchema } from "./validations";
import Link from "next/link";
import { forgotPassword } from "@/http/auth";

export function ForgotPasswordPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSent, setIsSent] = React.useState(false);

  const {
    formState: { errors },
    register,
    handleSubmit,
  } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    try {
      setIsLoading(true);
      await forgotPassword({ email: data.email });
      setIsSent(true);
    } catch (error: unknown) {
      const description =
        (
          error as {
            response?: {
              data?: { message?: string; errors?: Array<{ message: string }> };
            };
          }
        )?.response?.data?.message ||
        (
          error as {
            response?: {
              data?: { message?: string; errors?: Array<{ message: string }> };
            };
          }
        )?.response?.data?.errors?.[0]?.message ||
        "Não foi possível enviar o e-mail. Tente novamente.";
      toast({ title: "Erro", description, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <>
      <div className="hidden lg:flex lg:w-1/2 bg-gray-50 items-center justify-center p-12">
        <div className="max-w-lg">
          <Image
            src="/saudeDaGenteLogo.png"
            alt="Logo"
            width={360}
            height={160}
          />
        </div>
      </div>

      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          {isSent ? (
            <div className="space-y-8 text-center">
              <p className="text-lg font-semibold mb-16">
                O link para a redefinição da sua senha
                <br />
                foi enviado para o seu e-mail!
              </p>
              <Link href="/">
                <Button className="w-full">Fazer login</Button>
              </Link>
            </div>
          ) : (
            <>
              <h2 className="text-3xl font-bold tracking-tight text-center">
                Recuperar senha
              </h2>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <Input
                  label="E-mail"
                  type="email"
                  placeholder="Digite seu email"
                  errorMessage={errors.email?.message}
                  {...register("email")}
                />

                <div className="space-y-4 pt-4">
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Enviando...</span>
                      </div>
                    ) : (
                      "Enviar"
                    )}
                  </Button>
                </div>
              </form>
              <div className="text-center">
                <Link
                  href="/"
                  className="text-sm text-primary font-bold hover:text-primary/80 transition-colors"
                >
                  Voltar para o login
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}
