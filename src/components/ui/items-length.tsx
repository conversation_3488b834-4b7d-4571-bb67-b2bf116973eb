import { Select } from "./selectUi";

type ItemsLengthProps = {
  totalItems: number;
  itemsPerPage: number;
  onChange: (itemsPerPage: number) => void;
};

export function ItemsLength({
  totalItems,
  itemsPerPage,
  onChange,
}: ItemsLengthProps) {
  return (
    <Select
      className="w-[180px]"
      options={[
        {
          label: "10 por página",
          value: "10",
        },
        {
          label: "25 por página",
          value: "25",
        },
        {
          label: "50 por página",
          value: "50",
        },
        {
          label: "100 por página",
          value: "100",
        },
      ]}
      value={{ label: `${itemsPerPage} por página`, value: String(totalItems) }}
      onChange={(v) => {
        onChange(Number(v.value));
      }}
    />
  );
}
