import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
  PaginationStart,
  PaginationEnd,
} from "@/components/ui/pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TableProps } from "@/types/table";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

export function DataTable<T>({
  columns,
  data,
  keyExtractor,
  className,
  pagination,
  openFilterModal,
  isLoading,
  firstColumnClassName,
}: TableProps<T>) {
  return (
    <div
      className={`flex flex-col min-h-[calc(100vh-8rem)] gap-4 ${className}`}
    >
      <div className="rounded-sm">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column, index) => (
                <TableHead
                  key={index}
                  className={cn(
                    "px-4",
                    index === 0 ? firstColumnClassName : undefined
                  )}
                >
                  {column.isActionColumn ? (
                    <div className="w-4 flex justify-center">
                      {openFilterModal}
                    </div>
                  ) : (
                    column.header
                  )}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Loading skeleton rows
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  {columns.map((_, columnIndex) => (
                    <TableCell
                      key={columnIndex}
                      className={cn(
                        "px-2",
                        columnIndex === 0 ? firstColumnClassName : undefined
                      )}
                    >
                      <Skeleton className="h-4 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="text-center py-8 text-gray-500"
                >
                  Nenhum registro encontrado
                </TableCell>
              </TableRow>
            ) : (
              data.map((item) => (
                <TableRow key={keyExtractor(item)}>
                  {columns.map((column, index) => (
                    <TableCell
                      key={index}
                      className={cn(
                        "px-2 max-w-[160px] truncate",
                        index === 0 ? firstColumnClassName : undefined
                      )}
                    >
                      {column.render
                        ? column.render(item)
                        : column.accessor
                        ? (item[column.accessor] as React.ReactNode)
                        : null}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <div className="mt-auto flex w-full items-center justify-center py-4">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700">Exibir</span>
              <div className="w-fit">
                <select
                  className="h-9 px-3 w-auto border rounded-sm shadow-sm text-sm"
                  value={pagination.pageSize}
                  onChange={(e) =>
                    pagination.onPageSizeChange(Number(e.target.value))
                  }
                >
                  {[10, 25, 50, 100].map((n) => (
                    <option key={n} value={n}>
                      {n} por página
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationStart
                    href="#"
                    onClick={() => pagination.onPageChange(1)}
                    aria-disabled={pagination.currentPage === 1}
                    className={
                      pagination.currentPage === 1
                        ? "pointer-events-none opacity-50"
                        : undefined
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={() =>
                      pagination.onPageChange(
                        Math.max(1, pagination.currentPage - 1)
                      )
                    }
                    aria-disabled={pagination.currentPage === 1}
                    className={
                      pagination.currentPage === 1
                        ? "pointer-events-none opacity-50"
                        : undefined
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <span className="text-sm text-teal-600 px-2 font-semibold">
                    {pagination.currentPage}
                  </span>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={() =>
                      pagination.onPageChange(
                        Math.min(
                          pagination.totalPages,
                          pagination.currentPage + 1
                        )
                      )
                    }
                    aria-disabled={
                      pagination.currentPage === pagination.totalPages
                    }
                    className={
                      pagination.currentPage === pagination.totalPages
                        ? "pointer-events-none opacity-50"
                        : undefined
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <PaginationEnd
                    href="#"
                    onClick={() =>
                      pagination.onPageChange(pagination.totalPages)
                    }
                    aria-disabled={
                      pagination.currentPage === pagination.totalPages
                    }
                    className={
                      pagination.currentPage === pagination.totalPages
                        ? "pointer-events-none opacity-50"
                        : undefined
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
}
