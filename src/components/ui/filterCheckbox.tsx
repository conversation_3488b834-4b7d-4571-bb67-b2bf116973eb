"use client";

import { useState } from "react";
import { Checkbox } from "./checkbox";
import { Input } from "./input";
import { Label } from "./label";
import { LabelAndValue } from "@/types/labelAndValue";

interface CheckboxListProps {
  title: string;
  options: LabelAndValue[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  hideSearchBar?: boolean;
  hideHeader?: boolean;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  isLoading?: boolean;
  emptyMessage?: string;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
}

export function CheckboxList({
  title,
  options,
  selectedValues,
  onSelectionChange,
  hideSearchBar = false,
  hideHeader = false,
  searchPlaceholder,
  searchValue,
  onSearchChange,
  isLoading = false,
  emptyMessage,
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
}: CheckboxListProps) {
  const [localSearchQuery, setLocalSearchQuery] = useState("");

  const currentSearch = searchValue ?? localSearchQuery;
  const filteredOptions = onSearchChange
    ? options
    : options.filter((option) =>
        option.label.toLowerCase().includes(currentSearch.toLowerCase())
      );

  const handleCheckboxChange = (value: string) => {
    if (selectedValues.includes(value)) {
      onSelectionChange(selectedValues.filter((v) => v !== value));
    } else {
      onSelectionChange([...selectedValues, value]);
    }
  };

  const handleClearFilter = () => {
    onSelectionChange([]);
  };

  return (
    <div className="w-full">
      {!hideHeader && (
        <div className="flex items-center justify-between mb-2">
          <span className="font-bold text-primary">{title}</span>

          <button
            onClick={handleClearFilter}
            className="text-xs text-muted-foreground/60 hover:underline"
          >
            Limpar filtro
          </button>
        </div>
      )}

      <div className="space-y-4">
        {!hideSearchBar && (
          <Input
            type="text"
            placeholder={
              searchPlaceholder || `Filtre por ${title.toLowerCase()}`
            }
            value={currentSearch}
            onChange={(e) =>
              onSearchChange
                ? onSearchChange(e.target.value)
                : setLocalSearchQuery(e.target.value)
            }
            className="w-full rounded-sm"
          />
        )}

        <div
          className="space-y-2.5 max-h-[160px] overflow-y-auto pb-4"
          onScroll={(e) => {
            const el = e.currentTarget;
            const nearBottom =
              el.scrollTop + el.clientHeight >= el.scrollHeight - 24;
            if (nearBottom && onLoadMore && hasMore && !isLoadingMore)
              onLoadMore();
          }}
        >
          {isLoading ? (
            <div className="text-xs text-gray-500">Carregando...</div>
          ) : filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <div key={option.value} className="flex items-center gap-2">
                <Checkbox
                  className="h-5 w-5"
                  id={option.value}
                  checked={selectedValues.includes(option.value)}
                  onCheckedChange={() => handleCheckboxChange(option.value)}
                />
                <Label
                  htmlFor={option.value}
                  className="text-sm text-gray-700 cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))
          ) : currentSearch.trim().length > 0 ? (
            <div className="text-xs text-gray-500">
              {emptyMessage || "Nenhuma opção encontrada"}
            </div>
          ) : null}
          {isLoadingMore && (
            <div className="text-xs text-gray-500">Carregando...</div>
          )}
        </div>
      </div>
    </div>
  );
}
