import {
  Pagination as <PERSON><PERSON><PERSON><PERSON>had<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ation<PERSON>ontent,
  <PERSON>gin<PERSON><PERSON><PERSON><PERSON>,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
  PaginationStart,
  PaginationEnd,
  PaginationLink,
} from "@/components/ui/pagination";

type PaginationProps = {
  page: number;
  totalPages: number;
  onChange: (page: number) => void;
};

export function Pagination({ page, totalPages, onChange }: PaginationProps) {
  return (
    <div className="flex justify-center self-start w-full">
      <PaginationShadcnui>
        <PaginationContent>
          <PaginationItem>
            <PaginationStart
              href="#"
              onClick={() => onChange(1)}
              aria-disabled={page === 1}
              className={
                page === 1 ? "pointer-events-none opacity-50" : undefined
              }
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={() => onChange(Math.max(1, page - 1))}
              aria-disabled={page === 1}
              className={
                page === 1 ? "pointer-events-none opacity-50" : undefined
              }
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink isActive href="#" onClick={() => onChange(page)}>
              {page}
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={() => onChange(Math.min(totalPages, page + 1))}
              aria-disabled={page === totalPages}
              className={
                page === totalPages
                  ? "pointer-events-none opacity-50"
                  : undefined
              }
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationEnd
              href="#"
              onClick={() => onChange(totalPages)}
              aria-disabled={page === totalPages}
              className={
                page === totalPages
                  ? "pointer-events-none opacity-50"
                  : undefined
              }
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
        </PaginationContent>
      </PaginationShadcnui>
    </div>
  );
}
