"use client";

import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import * as React from "react";
import { Matcher } from "react-day-picker";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Calendar } from "./calendar";
import { Button } from "./button";

type DatePickerProps = {
  value?: Date;
  onChange: (date: Date) => void;
  errorMessage?: string;
  placeholder?: string;
  label?: string;
  classNameButton?: string;
  disabled?: boolean;
  disabledDate?: Matcher | Matcher[] | undefined;
  modal?: boolean;
  endMonth?: Date;
  startMonth?: Date;
};

export function DatePicker({
  modal,
  value,
  onChange,
  errorMessage,
  label,
  placeholder = "Selecione uma data",
  classNameButton,
  disabled,
  disabledDate,
  endMonth,
  startMonth,
}: DatePickerProps) {
  return (
    <Popover modal={modal}>
      <div className="flex h-20 flex-col items-start">
        {label && (
          <label className="mb-0.5 text-sm" htmlFor={label}>
            {label}
          </label>
        )}
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            data-empty={!value}
            disabled={disabled}
            className={cn(
              "flex h-11 w-full justify-start font-light whitespace-nowrap rounded-lg px-2.5",
              classNameButton
            )}
          >
            {value ? (
              format(value, "PPP", { locale: ptBR })
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <span className="text-red-500 text-sm">{errorMessage}</span>
      </div>
      <PopoverContent className="w-auto p-0">
        <Calendar
          captionLayout="dropdown"
          mode="single"
          required={false}
          selected={value}
          onSelect={(date) => date && onChange(date)}
          locale={ptBR}
          disabled={disabledDate}
          endMonth={endMonth}
          startMonth={startMonth}
          showOutsideDays={false}
        />
      </PopoverContent>
    </Popover>
  );
}
