"use client";

import { cn } from "@/lib/utils";
import { LabelAndValue } from "@/types/labelAndValue";
import { Check, ChevronDown, Search, X } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { Label } from "./label";

interface SearchableMultiSelectProps {
  options: LabelAndValue[];
  value: LabelAndValue[];
  onChange: (options: LabelAndValue[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  disabled?: boolean;
  className?: string;
  maxDisplayTags?: number;
  label?: string;
  errorMessage?: string;
  onCreateOption?: (name: string) => void;
  createLabel?: string;
}

export const SearchableMultiSelect = ({
  options,
  value,
  onChange,
  placeholder = "Selecione as opções...",
  searchPlaceholder = "Pesquisar...",
  disabled = false,
  className,
  maxDisplayTags = 4,
  label,
  errorMessage,
  onCreateOption,
  createLabel = "+ Criar novo",
}: SearchableMultiSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelect = (option: LabelAndValue) => {
    const isSelected = value?.some((item) => item.value === option.value);
    if (isSelected) {
      onChange((value || []).filter((item) => item.value !== option.value));
    } else {
      onChange([...(value || []), option]);
    }
  };

  const removeOption = (optionToRemove: LabelAndValue, e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(value.filter((option) => option.value !== optionToRemove.value));
  };

  const handleSelectAll = () => {
    if (value.length === options.length) {
      onChange([]);
      return;
    }
    onChange(options);
  };

  const displayTags = () => {
    if (value?.length === 0) return placeholder;
    if (value?.length <= maxDisplayTags) {
      return (
        <div className="flex flex-wrap gap-1">
          {value?.map((option) => (
            <span
              key={option.value}
              className="inline-flex items-center px-2 py-0.5 rounded bg-blue-100 text-primary text-sm"
            >
              {option.label}
              <div
                onClick={(e) => removeOption(option, e)}
                className="ml-1 hover:text-primary"
              >
                <X className="h-3 w-3" />
              </div>
            </span>
          ))}
        </div>
      );
    }
    return (
      <span className="text-gray-900">{value.length} opções selecionados</span>
    );
  };

  return (
    <div className={cn("relative w-full", className)} ref={selectRef}>
      {label && <Label className="text-sm">{label}</Label>}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={cn(
          "w-full min-h-[42px] px-4 py-2 text-left bg-white border rounded-sm shadow-sm",
          "flex items-center justify-between",
          "focus:outline-none focus:ring-1 focus:ring-gray-800/40",
          disabled && "bg-gray-100 cursor-not-allowed",
          "transition-colors duration-200"
        )}
        disabled={disabled}
      >
        <div
          className={
            !value?.length
              ? "text-gray-500 text-sm text-muted-foreground/50"
              : "text-gray-900"
          }
        >
          {displayTags()}
        </div>
        <ChevronDown
          className={cn(
            "w-4 h-4 transition-transform duration-200",
            isOpen ? "transform rotate-180" : ""
          )}
        />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg">
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder={searchPlaceholder}
                className="w-full pl-9 pr-4 py-2 border rounded-sm focus:outline-none"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
          <ul className="max-h-60 overflow-auto">
            <li
              key="all"
              className={cn(
                "px-4 py-2 cursor-pointer flex items-center",
                "hover:bg-blue-50"
              )}
              onClick={handleSelectAll}
            >
              <div
                className={cn(
                  "w-4 h-4 border rounded mr-2 flex items-center justify-center",
                  value.length === options.length
                    ? "bg-blue-500 border-blue-500"
                    : "border-gray-300"
                )}
              >
                {value.length === options.length && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
              <span className="text-gray-900 text-sm">Todos</span>
            </li>
            {filteredOptions.map((option) => {
              const isSelected = value.some(
                (item) => item.value === option.value
              );
              return (
                <li
                  key={option.value}
                  className={cn(
                    "px-4 py-2 cursor-pointer flex items-center",
                    "hover:bg-blue-50"
                  )}
                  onClick={() => handleSelect(option)}
                >
                  <div
                    className={cn(
                      "w-4 h-4 border rounded mr-2 flex items-center justify-center",
                      isSelected
                        ? "bg-blue-500 border-blue-500"
                        : "border-gray-300"
                    )}
                  >
                    {isSelected && <Check className="h-3 w-3 text-white" />}
                  </div>
                  <span className="text-gray-900 text-sm">{option.label}</span>
                </li>
              );
            })}
            {filteredOptions?.length === 0 && (
              <li className="px-4 py-2 text-gray-500 flex items-center justify-between">
                <span>Nenhum resultado</span>
                {onCreateOption && (
                  <button
                    type="button"
                    className="text-blue-600 hover:underline text-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onCreateOption(searchQuery.trim());
                    }}
                  >
                    {createLabel}
                  </button>
                )}
              </li>
            )}
          </ul>
        </div>
      )}
      {errorMessage && (
        <span className="text-destructive text-xs">{errorMessage}</span>
      )}
    </div>
  );
};
