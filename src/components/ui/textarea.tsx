"use client";
/* eslint-disable react/display-name */
import * as React from "react";

import { cn } from "@/lib/utils";
import { Label } from "./label";

interface TextareaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, "children"> {
  label?: string;
  errorMessage?: string;
  autoResize?: boolean;
}

const TextareaComponent = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, autoResize = true, onInput, ...props }, ref) => {
    const innerRef = React.useRef<HTMLTextAreaElement | null>(null);

    const setRefs = React.useCallback(
      (el: HTMLTextAreaElement | null) => {
        innerRef.current = el;
        if (typeof ref === "function") ref(el);
        else if (ref)
          (ref as React.MutableRefObject<HTMLTextAreaElement | null>).current =
            el;
      },
      [ref]
    );

    const adjustHeight = React.useCallback(() => {
      const el = innerRef.current;
      if (!el || !autoResize) return;
      el.style.height = "auto";
      el.style.height = `${el.scrollHeight}px`;
    }, [autoResize]);

    React.useEffect(() => {
      adjustHeight();
    }, [props.value, props.defaultValue, adjustHeight]);

    return (
      <textarea
        className={cn(
          "flex min-h-[84px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-base shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-800/40 disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-900 md:text-sm resize-none overflow-hidden",
          className
        )}
        ref={setRefs}
        onInput={(e) => {
          adjustHeight();
          onInput?.(e);
        }}
        {...props}
      />
    );
  }
);

function Textarea({ label, errorMessage, ...props }: TextareaProps) {
  return (
    <div className="flex flex-col gap-1 w-full">
      {label && <Label className="text-sm">{label}</Label>}
      <TextareaComponent {...props} />
      {errorMessage && (
        <span className="text-destructive text-xs">{errorMessage}</span>
      )}
    </div>
  );
}

export { Textarea };
