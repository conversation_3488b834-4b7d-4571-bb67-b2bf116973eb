import * as React from "react";

import { Input } from "./input";
import { LabelAndValue } from "@/types/labelAndValue";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "./command";
import { Loader2 } from "lucide-react";

type ComboboxProps = {
  label: string;
  placeholder: string;
  options: LabelAndValue[];
  value?: LabelAndValue;
  onChange: (value: LabelAndValue) => void;
  errorMessage?: string;
  disabled?: boolean;
  isLoading?: boolean;
};

export function Combobox({
  label,
  placeholder,
  options,
  value,
  onChange,
  errorMessage,
  disabled,
  isLoading,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger
        className="relative text-start w-full"
        disabled={disabled}
      >
        <Input
          label={label}
          value={value?.label || ""}
          errorMessage={errorMessage}
          disabled={disabled || isLoading}
          placeholder={placeholder}
          readOnly
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 bottom-6 translate-y-1/2">
            <Loader2 className="w-4 h-4 text-gray-400 animate-spin " />
          </div>
        )}
      </PopoverTrigger>

      <PopoverContent
        className="p-0"
        matchTriggerWidth
        onWheel={(e) => {
          e.stopPropagation();
        }}
      >
        <Command>
          <CommandInput placeholder="Pesquisar..." />
          <CommandList>
            <CommandEmpty>Não encontrado.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => {
                    onChange(option);
                    setOpen(false);
                  }}
                >
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
