import * as React from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Checkbox } from "./checkbox";
import { Input } from "./input";
import { LabelAndValue } from "@/types/labelAndValue";

type MultiselectComboboxProps = {
  label?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  options: LabelAndValue[];
  value?: LabelAndValue[];
  onChange: (value: LabelAndValue[]) => void;
  errorMessage?: string;
  disabled?: boolean;
  isLoading?: boolean;
  maxSelectedDisplay?: number;
  className?: string;
  selectAllLabel?: string;
  clearAllLabel?: string;
  showSelectAll?: boolean;
  showClearAll?: boolean;
  triggerClassname?: string;
  popoverClassname?: string;
};

export function MultiselectCombobox({
  label,
  placeholder = "Selecione opções...",
  searchPlaceholder = "Pesquisar...",
  emptyMessage = "Não encontrado.",
  options,
  value = [],
  onChange,
  errorMessage,
  disabled = false,
  maxSelectedDisplay = 3,
  className,
  selectAllLabel = "Selecionar todas",
  showSelectAll = true,
  showClearAll = true,
  triggerClassname = "w-full",
  popoverClassname = "",
}: MultiselectComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedValues = React.useMemo(
    () => new Set(value.map((item) => item.value)),
    [value]
  );

  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options;
    return options.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [options, searchValue]);

  const isAllSelected = React.useMemo(() => {
    return (
      filteredOptions.length > 0 &&
      filteredOptions.every((option) => selectedValues.has(option.value))
    );
  }, [filteredOptions, selectedValues]);

  const handleSelect = React.useCallback(
    (option: LabelAndValue) => {
      const newValue = selectedValues.has(option.value)
        ? value.filter((item) => item.value !== option.value)
        : [...value, option];
      onChange(newValue);
    },
    [value, selectedValues, onChange]
  );

  const handleSelectAll = React.useCallback(() => {
    if (isAllSelected) {
      const filteredValues = new Set(filteredOptions.map((opt) => opt.value));
      const newValue = value.filter((item) => !filteredValues.has(item.value));
      onChange(newValue);
    } else {
      const newItems = filteredOptions.filter(
        (option) => !selectedValues.has(option.value)
      );
      onChange([...value, ...newItems]);
    }
  }, [isAllSelected, filteredOptions, value, selectedValues, onChange]);

  const displayText = React.useMemo(() => {
    if (value.length === 0) return placeholder;
    if (value.length <= maxSelectedDisplay) {
      return value.map((item) => item.label).join(", ");
    }
    return `${value
      .slice(0, maxSelectedDisplay)
      .map((item) => item.label)
      .join(", ")} +${value.length - maxSelectedDisplay} mais`;
  }, [value, maxSelectedDisplay, placeholder]);

  return (
    <div
      className={cn(
        "flex flex-col gap-1.5 w-full items-end justify-end",
        className
      )}
    >
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger disabled={disabled} className="w-full text-start">
          <Input
            label={label}
            value={displayText}
            readOnly
            errorMessage={errorMessage}
            disabled={disabled}
            placeholder={placeholder}
            className={cn(
              "text-start h-11 truncate cursor-pointer w-full",
              value.length === 0 ? "text-muted-foreground" : "text-black",
              triggerClassname
            )}
          />
        </PopoverTrigger>

        <PopoverContent
          className={cn("p-0", popoverClassname)}
          onWheel={(e) => e.stopPropagation()}
          matchTriggerWidth
        >
          <Command>
            <CommandInput
              placeholder={searchPlaceholder}
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandList>
              <CommandEmpty>{emptyMessage}</CommandEmpty>
              <CommandGroup>
                {(showSelectAll || showClearAll) && (
                  <>
                    {showSelectAll && filteredOptions.length > 0 && (
                      <CommandItem
                        onSelect={handleSelectAll}
                        className="font-medium"
                      >
                        <Checkbox checked={isAllSelected} />
                        {selectAllLabel}
                      </CommandItem>
                    )}
                  </>
                )}

                {filteredOptions.map((option) => {
                  const isSelected = selectedValues.has(option.value);
                  return (
                    <CommandItem
                      key={option.value}
                      value={option.label}
                      onSelect={() => handleSelect(option)}
                    >
                      <Checkbox checked={isSelected} />
                      {option.label}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
