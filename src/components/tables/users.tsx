"use client";

import { useMemo, useState } from "react";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { DataTable } from "../ui/datatable";
import { Button } from "../ui/button";
import { Edit2, ListFilter } from "lucide-react";
import { Column } from "@/types/table";
import { UserModalForm } from "../pages/users/userModal";
import { FilterUserModal } from "../pages/users/filter";
import { getUsers } from "@/http/users";
import type { GetUsersResponse, UserListItem } from "@/http/users/types";
import { StatusBadge } from "@/components/ui/status-badge";
import { maskPhoneBR, translateUserRole } from "@/lib/format";

type UsersTableProps = {
  search?: string;
};

export function UsersTable({ search }: UsersTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<{
    consultancyIds?: string[];
    unionIds?: string[];
    businessGroupIds?: string[];
    businessUnitIds?: string[];
    roles?: string[];
    isActive?: boolean;
  }>({});

  const queryKey = useMemo(
    () => [
      "users",
      {
        page: currentPage,
        limit: pageSize,
        search,
        ...filters,
      },
    ],
    [currentPage, pageSize, search, filters]
  );

  const { data, isLoading } = useQuery<GetUsersResponse>({
    queryKey,
    queryFn: () =>
      getUsers({
        page: currentPage,
        limit: pageSize,
        search: search || undefined,
        consultancyIds: filters.consultancyIds,
        unionIds: filters.unionIds,
        businessGroupIds: filters.businessGroupIds,
        businessUnitIds: filters.businessUnitIds,
        roles: filters.roles,
        isActive: filters.isActive,
      }),
    placeholderData: keepPreviousData,
  });

  const users = data?.users ?? [];
  const totalPages = data?.totalPages ?? 1;

  const columns: Column<UserListItem>[] = [
    { header: "Nome", accessor: "name" },
    { header: "Email", accessor: "email" },
    { header: "Telefone", render: (person) => maskPhoneBR(person.phone || "") },
    { header: "Perfil", render: (person) => translateUserRole(person.role) },
    {
      header: "Status",
      render: (person) => <StatusBadge isActive={person.isActive} />,
    },
    {
      isActionColumn: true,
      header: "",
      render: (person) => (
        <UserModalForm
          isEditMode
          user={person}
          triggerButton={
            <Button className="w-7 h-7">
              <Edit2 size={4} className="w-2 h-2" />
            </Button>
          }
        />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={users}
      keyExtractor={(item) => item.id}
      className="max-w-[calc(100vw-260px)] p-8"
      openFilterModal={
        <FilterUserModal
          triggerButton={
            <Button variant="ghost" className="px-2 h-8 border border-gray-200">
              <ListFilter />
            </Button>
          }
          onApply={(f) => {
            setFilters(f);
            setCurrentPage(1);
          }}
        />
      }
      isLoading={isLoading}
      pagination={{
        currentPage,
        totalPages,
        onPageChange: setCurrentPage,
        pageSize,
        onPageSizeChange: (newSize) => {
          setPageSize(newSize);
          setCurrentPage(1);
        },
      }}
    />
  );
}
