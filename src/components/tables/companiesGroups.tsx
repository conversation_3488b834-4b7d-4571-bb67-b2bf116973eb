"use client";

import { useState } from "react";
import { DataTable } from "../ui/datatable";
import { Button } from "../ui/button";
import { Edit2 } from "lucide-react";
import { Column } from "@/types/table";
import { useQuery } from "@tanstack/react-query";
import { getBusinessGroups } from "@/http/business-groups";
import { formatCNPJ, maskPhoneBR } from "@/lib/format";
import { CompaniesGroupModalForm } from "../pages/companiesGroups/companiesGroupsModal";
import { CompaniesGroup } from "@/types/companies-groups";

type CompaniesGroupsTableProps = { search?: string };

export function CompaniesGroupsTable({
  search = "",
}: CompaniesGroupsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const { data, isLoading } = useQuery({
    queryKey: ["business-groups", currentPage, pageSize, search],
    queryFn: () =>
      getBusinessGroups({
        page: currentPage,
        limit: pageSize,
        search: search || undefined,
      }),
  });

  const totalPages = data?.totalPages || 0;

  const columns: Column<CompaniesGroup>[] = [
    { header: "CNPJ", render: (r) => formatCNPJ(r.cnpj) },
    { header: "Nome fantasia", accessor: "alias" },
    { header: "Nome do contato", render: (r) => r.contact.name },
    { header: "Email do contato", render: (r) => r.contact.email },
    {
      header: "Telefone do contato",
      render: (r) => maskPhoneBR(r.contact.phone),
    },
    {
      isActionColumn: true,
      header: "",
      render: (row) => (
        <CompaniesGroupModalForm
          isEditMode
          id={row.id}
          prefill={row}
          triggerButton={
            <Button className="w-7 h-7">
              <Edit2 size={16} className="w-4 h-4" />
            </Button>
          }
        />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={data?.businessGroups || []}
      keyExtractor={(item) => item.id}
      className="max-w-[calc(100vw-260px)] p-8"
      isLoading={isLoading}
      pagination={{
        currentPage,
        totalPages,
        onPageChange: setCurrentPage,
        pageSize,
        onPageSizeChange: (newSize) => {
          setPageSize(newSize);
          setCurrentPage(1);
        },
      }}
    />
  );
}
