"use client";

import {
  Sidebar,
  Sidebar<PERSON>ontent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Image from "next/image";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { items } from "./data";

export function AppSidebar() {
  const { user } = useAuth();

  if (!user) return null;

  return (
    <Sidebar title="Sidebar">
      <SidebarContent className="bg-primary py-6">
        <SidebarGroup className="flex flex-col items-center gap-10 w-full">
          <SidebarGroupLabel>
            <div className="relative w-24 h-10">
              <Image
                src="/saudeDaGente_white.png"
                alt="Logo"
                fill
                objectFit="contain"
                className="absolute"
              />
            </div>
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="flex items-center text-center gap-5">
              {items
                .filter(
                  (item) => !item.roles || item.roles.includes(user?.role)
                )
                .map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      className="[&>svg]:size-5 hover:text-primary hover:font-bold"
                    >
                      <Link href={item.url} className="text-white">
                        <item.icon strokeWidth={2} />
                        <span className="w-40">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
