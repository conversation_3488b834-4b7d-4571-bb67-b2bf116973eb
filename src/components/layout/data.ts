import { UserRole } from "@/types/roles";
import {
  Building2,
  Calculator,
  Receipt,
  Store,
  UserCog,
  Users,
} from "lucide-react";

type MenuItems = {
  title: string;
  url: string;
  icon: React.ElementType;
  roles?: UserRole[];
};

export const items: MenuItems[] = [
  {
    title: "Grupos Empresariais",
    url: "/grupos-empresariais",
    icon: Building2,
    roles: ["ADMIN_SDG", "CONTROLLER_SDG", "ATTENDANT_SDG"],
  },
  {
    title: "Unidades",
    url: "/gestao-de-unidades",
    icon: Store,
    roles: ["ADMIN_SDG", "CONTROLLER_SDG", "ATTENDANT_SDG"],
  },
  {
    title: "Contabilidades",
    url: "/contabilidades",
    icon: Calculator,
    roles: ["ADMIN_SDG", "CONTROLLER_SDG"],
  },
  {
    title: "Sindicatos",
    url: "/sindicatos",
    icon: Users,
    roles: ["ADMIN_SDG", "CONTROLLER_SDG"],
  },
  {
    title: "Usuários",
    url: "/usuarios",
    icon: UserCog,
  },
  {
    title: "Faturamento",
    url: "/faturamento",
    icon: Receipt,
  },
];
