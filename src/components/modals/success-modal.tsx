"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Check } from "lucide-react";

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message?: string;
}

export function SuccessModal({
  isOpen,
  onClose,
  title,
  message,
}: SuccessModalProps) {
  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) onClose();
      }}
    >
      <DialogContent className="max-w-xl p-12">
        <DialogTitle className="text-3xl font-semibold text-gray-900 mb-4 text-center">
          {title}
        </DialogTitle>

        <div className="flex flex-col items-center text-center">
          <div className="w-28 h-28 rounded-full border-8 border-green-500 flex items-center justify-center mb-8">
            <Check className="w-16 h-16 text-green-500" strokeWidth={3} />
          </div>

          {message && <p className="text-gray-600 mb-10">{message}</p>}

          <Button
            onClick={onClose}
            variant="secondary"
            size="lg"
            className="w-full"
          >
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
