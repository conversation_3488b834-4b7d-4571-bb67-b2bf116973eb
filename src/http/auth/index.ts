import { publicApi, api } from "@/services/api";
import {
  ISignIn,
  IRegister,
  IForgotPassword,
  IResetPassword,
  IRefreshToken,
} from "./types";

export function signIn({ email, password }: ISignIn) {
  const response = publicApi.post("/api/auth/login", {
    email,
    password,
  });

  return response;
}

export function register({
  email,
  name,
  phone,
  role,
  unionIds,
  businessGroupIds,
  businessUnitIds,
  consultancyIds,
}: IRegister) {
  const response = api.post("/api/auth/register", {
    email,
    name,
    phone,
    role,
    unionIds,
    businessGroupIds,
    businessUnitIds,
    consultancyIds,
  });

  return response;
}

export function forgotPassword({ email }: IForgotPassword) {
  const response = publicApi.post("/api/auth/forgot-password", {
    email,
  });

  return response;
}

export function resetPassword({
  token,
  password,
  confirmPassword,
}: IResetPassword) {
  const response = publicApi.post("/api/auth/reset-password", {
    token,
    password,
    confirmPassword,
  });

  return response;
}

export function refreshToken({ refreshToken }: IRefreshToken) {
  const response = publicApi.post("/api/auth/refresh-token", {
    refreshToken,
  });

  return response;
}
