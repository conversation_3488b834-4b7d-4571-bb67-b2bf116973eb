export type BillingBatchItem = {
  id: string;
  sequenceNumber: number;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
  processedInvoices?: number;
  failedInvoices?: number;
  totalAmount?: number;
  requestedBy?: string;
  requestedByEmail?: string;
  generatedAt: string;
  createdAt?: string;
  updatedAt?: string;
  totalInvoices?: number;
  totalFailedInvoices?: number;
  totalSuccessfulInvoices?: number;
  progress?: number;
};

export type GetBatchesResponse = {
  data: BillingBatchItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

export type CreateBillingBatchRequest = {
  selectedUnionIds: string[];
  selectedBusinessGroupIds: string[];
  selectedBusinessUnitIds: string[];
  baseDueDayStart: number;
  baseDueDayEnd: number;
  invoiceDueDate: string;
  maintainDueDate?: boolean;
};

export type CreateBillingBatchResponse = {
  batchId: string;
  message: string;
};

export type GetBatchDetailsResponse = {
  batch: {
    id: string;
    sequenceNumber: number;
    generatedAt: string;
    status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
    processedInvoices: number;
    failedInvoices: number;
    totalAmount: number;
    totalExpectedInvoices: number;
    invoices: Array<{
      id: string;
      amount: number;
      employeeCount: number;
      dueDate: string;
      generatedAt: string;
      status: "PENDING" | "PAID" | "OVERDUE" | "CANCELLED";
      generationStatus: "SUCCESS" | "ERROR";
      unionName: string;
      businessGroupName: string;
      businessUnitName: string;
    }>;
  };
};

export type GetBatchInvoicesParams = {
  batchId: string;
  page?: number;
  limit?: number;
  statuses?: Array<"PENDING" | "PAID" | "OVERDUE" | "CANCELLED">;
  minAmount?: number;
  maxAmount?: number;
  dueDateFrom?: string;
  dueDateTo?: string;
  generatedDateFrom?: string;
  generatedDateTo?: string;
};

export type GetBatchInvoicesResponse = {
  invoices: Array<Record<string, unknown>>;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type ChangeDueDateRequest = {
  invoiceIds: string[];
  newDueDate: string;
};

export type UpdateInvoicesStatusRequest = {
  invoiceIds: string[];
  status: "PENDING" | "PAID" | "OVERDUE" | "CANCELLED";
  paidAt?: string;
};

export type SendSecondCopyRequest = {
  invoiceIds: string[];
};
