/* eslint-disable @typescript-eslint/no-explicit-any */
import { api } from "@/services/api";
import { withRetry } from "@/lib/utils";
import type {
  BillingBatchItem,
  GetBatchesResponse,
  CreateBillingBatchRequest,
  CreateBillingBatchResponse,
  GetBatchDetailsResponse,
  GetBatchInvoicesParams,
  GetBatchInvoicesResponse,
  ChangeDueDateRequest,
  UpdateInvoicesStatusRequest,
  SendSecondCopyRequest,
} from "./types";

export async function getBillingBatchesV2(params?: {
  page?: number;
  limit?: number;
}) {
  const search = new URLSearchParams();
  if (params?.page) search.append("page", params.page.toString());
  if (params?.limit) search.append("limit", params.limit.toString());

  const response = await api.get(`/api/billing/batches?${search.toString()}`);

  return response.data;
}

export async function getBillingBatches(params?: {
  page?: number;
  limit?: number;
}): Promise<GetBatchesResponse> {
  const search = new URLSearchParams();
  if (params?.page) search.append("page", params.page.toString());
  if (params?.limit) search.append("limit", params.limit.toString());
  const { data } = await withRetry(
    () => api.get(`/api/billing/batches?${search.toString()}`),
    { retries: 2, baseDelayMs: 400 }
  );

  // Handle the actual API response structure
  const batches = (data as any)?.invoiceBatches || [];
  const total = (data as any)?.total || 0;
  const page = (data as any)?.page || params?.page || 1;
  const limit = (data as any)?.limit || params?.limit || 10;
  const totalPages = (data as any)?.totalPages || 1;

  const normalized: GetBatchesResponse = {
    data: batches as BillingBatchItem[],
    pagination: {
      page,
      limit,
      total,
      totalPages,
    },
  };

  return normalized;
}

export async function createBillingBatch(
  body: CreateBillingBatchRequest
): Promise<CreateBillingBatchResponse> {
  const { data } = await api.post("/api/billing/batches", body);
  return data as CreateBillingBatchResponse;
}

export async function getBillingBatchDetails(
  batchId: string
): Promise<GetBatchDetailsResponse> {
  const { data } = await withRetry(
    () => api.get(`/api/billing/batches/${batchId}`),
    { retries: 2, baseDelayMs: 400 }
  );
  return data as GetBatchDetailsResponse;
}

export async function getBatchInvoices(
  params: GetBatchInvoicesParams
): Promise<GetBatchInvoicesResponse> {
  const {
    batchId,
    page,
    limit,
    statuses,
    minAmount,
    maxAmount,
    dueDateFrom,
    dueDateTo,
    generatedDateFrom,
    generatedDateTo,
  } = params;

  const search = new URLSearchParams();
  if (page) search.append("page", page.toString());
  if (limit) search.append("limit", limit.toString());
  if (statuses?.length) search.append("statuses", statuses.join(","));
  if (minAmount !== undefined) search.append("minAmount", String(minAmount));
  if (maxAmount !== undefined) search.append("maxAmount", String(maxAmount));
  if (dueDateFrom) search.append("dueDateFrom", dueDateFrom);
  if (dueDateTo) search.append("dueDateTo", dueDateTo);
  if (generatedDateFrom) search.append("generatedDateFrom", generatedDateFrom);
  if (generatedDateTo) search.append("generatedDateTo", generatedDateTo);

  const { data } = await api.get(
    `/api/billing/batches/${batchId}/invoices?${search.toString()}`
  );
  return data as GetBatchInvoicesResponse;
}

export async function changeInvoicesDueDate(
  body: ChangeDueDateRequest
): Promise<{ success?: boolean }> {
  const { data } = await api.post(
    `/api/billing/invoices/change-due-date`,
    body
  );
  return data as { success?: boolean };
}

export async function updateInvoicesStatus(
  body: UpdateInvoicesStatusRequest
): Promise<{ success?: boolean }> {
  const { data } = await api.post(`/api/billing/invoices/update-status`, body);
  return data as { success?: boolean };
}

export async function sendInvoicesSecondCopy(
  body: SendSecondCopyRequest
): Promise<{ success?: boolean }> {
  const { data } = await api.post(
    `/api/billing/invoices/send-second-copy`,
    body
  );
  return data as { success?: boolean };
}

export type { BillingBatchItem } from "./types";
