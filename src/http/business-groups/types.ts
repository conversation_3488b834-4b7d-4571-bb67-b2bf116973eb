import { IGetConsultancies } from "../consultancies/types";
import { Union } from "../unions";

export type IAddress = {
  street: string;
  number: string;
  complement?: string;
  zip: string;
  neighborhood: string;
  city: string;
  state: string;
};

export type IContact = {
  name: string;
  phone: string;
  email: string;
};

export type ICreateBusinessGroup = {
  unionId: string;
  consultancyId?: string;
  cnpj: string;
  alias: string;
  name: string;
  address: IAddress;
  cnae: string;
  contact: IContact;
  financeContact?: Partial<IContact>;
};

export type BusinessGroup = {
  id: string;
  union: Union;
  consultancy: IGetConsultancies;
  cnpj: string;
  alias: string;
  name: string;
  address: IAddress;
  cnae: string;
  contact: IContact;
  financeContact: IContact;
  createdAt: string;
  updatedAt: string;
};

export type BusinessGroupResponse = {
  businessGroups: BusinessGroup[];
  total: number;
  page: number;
  limit: number;
};

export type IGetBusinessGroups = {
  page?: number;
  limit?: number;
  search?: string;
  unionIds?: string[];
  consultancyIds?: string[];
};

export type IGetBusinessGroupById = {
  id: string;
};

export type IUpdateBusinessGroup = {
  id: string;
  alias?: string;
  contact?: Partial<IContact>;
  financeContact?: Partial<IContact>;
  consultancyId?: string;
};
