import { api } from "@/services/api";
import {
  Union,
  CreateUnionRequest,
  EditUnionRequest,
  GetUnionsParams,
  GetUnionsResponse,
  CreateUnionResponse,
} from "./types";

export const getUnions = async (
  params: GetUnionsParams = {}
): Promise<GetUnionsResponse> => {
  const searchParams = new URLSearchParams();

  if (params.page) searchParams.append("page", params.page.toString());
  if (params.limit) searchParams.append("limit", params.limit.toString());
  if (params.search) searchParams.append("search", params.search);

  const doRequest = async () =>
    api.get(`/api/unions?${searchParams.toString()}`).then((r) => r.data);

  try {
    const { withRetry } = await import("@/lib/utils");
    return await withRetry(doRequest, { retries: 2, baseDelayMs: 400 });
  } catch (error) {
    // rethrow to let callers decide how to handle
    throw error;
  }
};

export const getUnionById = async (id: string): Promise<Union> => {
  const response = await api.get(`/api/unions/${id}`);
  return response.data;
};

export const createUnion = async (
  data: CreateUnionRequest
): Promise<CreateUnionResponse> => {
  const response = await api.post("/api/unions", data);
  return response.data;
};

export const updateUnion = async (
  id: string,
  data: EditUnionRequest
): Promise<{ id: string }> => {
  const response = await api.put(`/api/unions/${id}`, data);
  return response.data;
};

export * from "./types";
