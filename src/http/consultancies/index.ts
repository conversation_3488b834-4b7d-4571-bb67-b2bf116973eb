import { api } from "@/services/api";
import {
  IGetConsultancies,
  ICreateConsultancy,
  IGetConsultancyById,
  IUpdateConsultancy,
  GetConsultanciesResponse,
  Consultancy,
} from "./types";

export async function getConsultancies({
  page,
  limit,
  search,
  isActive,
}: IGetConsultancies): Promise<GetConsultanciesResponse> {
  const params = new URLSearchParams();

  if (page !== undefined) params.append("page", page.toString());
  if (limit !== undefined) params.append("limit", limit.toString());
  if (search) params.append("search", search);
  if (isActive !== undefined) params.append("isActive", isActive.toString());

  const response = await api.get(`/api/consultancies?${params.toString()}`);

  return response.data;
}

export function createConsultancy({
  cnpj,
  alias,
  name,
  address,
  contact,
  isActive,
}: ICreateConsultancy) {
  const response = api.post("/api/consultancies", {
    cnpj,
    alias,
    name,
    address,
    contact,
    isActive,
  });

  return response;
}

export function getConsultancyById({ id }: IGetConsultancyById) {
  const response = api.get<Consultancy>(`/api/consultancies/${id}`);

  return response;
}

export function updateConsultancy({
  id,
  alias,
  name,
  address,
  contact,
  isActive,
}: IUpdateConsultancy) {
  const response = api.put(`/api/consultancies/${id}`, {
    alias,
    name,
    address,
    contact,
    isActive,
  });

  return response;
}
