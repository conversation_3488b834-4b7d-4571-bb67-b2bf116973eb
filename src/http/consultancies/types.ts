export type IAddress = {
  street: string;
  number: string;
  complement?: string;
  zip: string;
  neighborhood: string;
  city: string;
  state: string;
};

export type IContact = {
  name: string;
  phone: string;
  email: string;
};

export type IGetConsultancies = {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
};

export type ICreateConsultancy = {
  cnpj: string;
  alias: string;
  name: string;
  address: IAddress;
  contact: IContact;
  isActive?: boolean;
};

export type IGetConsultancyById = {
  id: string;
};

export type IUpdateConsultancy = {
  id: string;
  alias?: string;
  name?: string;
  address?: Partial<IAddress>;
  contact?: Partial<IContact>;
  isActive?: boolean;
};

export type Consultancy = {
  id: string;
  cnpj: string;
  alias: string;
  name: string;
  address: IAddress;
  contact: IContact;
  isActive: boolean;
};

export type GetConsultanciesResponse = {
  consultancies: Consultancy[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};
