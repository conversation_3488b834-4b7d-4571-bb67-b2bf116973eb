export type IAddress = {
  street: string;
  number: string;
  complement?: string;
  zip: string;
  neighborhood: string;
  city: string;
  state: string;
};

export type BusinessUnit = {
  id: string;
  cnpj: string;
  alias: string;
  name: string;
  dueDay: number;
  businessGroup: { id: string; alias: string; name: string };
  businessUnitPlans: {
    createdAt: string;
    employeeCount: number;
    id: string;
    name: string;
    planId: string;
    updatedAt: string;
  }[];
};

export type BusinessUnitResponse = {
  businessUnits: BusinessUnit[];
  total: number;
  page: number;
  limit: number;
};

export type IPlan = {
  planId: string;
  employeeCount: number;
};

export type ICreateBusinessUnit = {
  businessGroupId: string;
  cnpj: string;
  alias: string;
  name: string;
  address: IAddress;
  cnae: string;
  dueDay: number;
  plans?: IPlan[];
};

export type IGetBusinessUnits = {
  page?: number;
  limit?: number;
  search?: string;
  businessGroupIds?: string[];
  dueDayStart?: number;
  dueDayEnd?: number;
  employeeCountStart?: number;
  employeeCountEnd?: number;
};

export type IGetBusinessUnitById = {
  id: string;
};

export type IUpdateBusinessUnit = {
  id: string;
  alias: string;
  name: string;
  address: IAddress;
  cnae: string;
  dueDay: number;
  plans?: IPlan[];
};
