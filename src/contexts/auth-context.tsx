"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { signIn as signInService } from "@/http/auth";
import { useToast } from "@/hooks/use-toast";

// Types
type UserRole =
  | "ADMIN_CLIENT"
  | "ADMIN_SDG"
  | "ATTENDANT_SDG"
  | "ACCOUNTANT"
  | "CONTROLLER_SDG"
  | "FINANCIAL_CLIENT"
  | "FINANCIAL_SDG";

//

interface User {
  id: string;
  email: string;
  name: string;
  phone: string;
  role: UserRole;
  isActive: boolean;
  unions: Array<{ id: string; name: string }>;
  businessGroups: Array<{ id: string; name: string }>;
  businessUnits: Array<{ id: string; name: string }>;
  consultancies: Array<{ id: string; name: string }>;
  createdAt: string;
  updatedAt: string;
}

interface AuthContextData {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => void;
  refreshUser: () => Promise<void>;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

// Storage keys
const ACCESS_TOKEN_KEY = "@gestao-sindicatos:access-token";
const REFRESH_TOKEN_KEY = "@gestao-sindicatos:refresh-token";
const USER_KEY = "@gestao-sindicatos:user";

// Utility functions for secure storage
const storage = {
  setItem: (key: string, value: string) => {
    if (typeof window !== "undefined") {
      localStorage.setItem(key, value);
    }
  },
  getItem: (key: string): string | null => {
    if (typeof window !== "undefined") {
      return localStorage.getItem(key);
    }
    return null;
  },
  removeItem: (key: string) => {
    if (typeof window !== "undefined") {
      localStorage.removeItem(key);
    }
  },
  clear: () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
      localStorage.removeItem(USER_KEY);
    }
  },
};

// Create context
const AuthContext = createContext<AuthContextData>({} as AuthContextData);

// Custom hook to use auth context
export function useAuth(): AuthContextData {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}

// Auth Provider Component
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const isAuthenticated = !!user;

  // Load user from storage on mount
  useEffect(() => {
    const loadStoredUser = () => {
      try {
        const storedUser = storage.getItem(USER_KEY);
        const storedAccessToken = storage.getItem(ACCESS_TOKEN_KEY);

        if (storedUser && storedAccessToken) {
          setUser(JSON.parse(storedUser));
        }
      } catch (error) {
        console.error("Error loading stored user:", error);
        storage.clear();
      } finally {
        setIsLoading(false);
      }
    };

    loadStoredUser();
  }, []);

  // Sign in function
  const signIn = useCallback(
    async (email: string, password: string) => {
      try {
        setIsLoading(true);

        const response = await signInService({ email, password });

        const { user: userData, accessToken, refreshToken } = response.data;

        // Store tokens and user data
        storage.setItem(ACCESS_TOKEN_KEY, accessToken);
        storage.setItem(REFRESH_TOKEN_KEY, refreshToken);
        storage.setItem(USER_KEY, JSON.stringify(userData));

        setUser(userData);
      } catch (error: unknown) {
        const { extractApiErrorMessage } = await import("@/lib/utils");
        const errorMessage = extractApiErrorMessage(
          error,
          "Erro ao fazer login. Verifique suas credenciais."
        );

        toast({
          title: "Erro",
          description: errorMessage,
          variant: "destructive",
        });

        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [toast]
  );

  // Sign out function
  const signOut = useCallback(() => {
    storage.clear();
    setUser(null);

    toast({
      title: "Logout realizado",
      description: "Você foi desconectado com sucesso",
      variant: "success",
    });
  }, [toast]);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    try {
      if (!user?.id) return;

      // Implementar quando tivermos endpoint para buscar dados do usuário atual
      // const response = await api.get(`/users/${user.id}`);
      // const updatedUser = response.data.user;
      // storage.setItem(USER_KEY, JSON.stringify(updatedUser));
      // setUser(updatedUser);
    } catch (error) {
      console.error("Error refreshing user:", error);
    }
  }, [user?.id]);

  const contextValue: AuthContextData = {
    user,
    isLoading,
    isAuthenticated,
    signIn,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}
