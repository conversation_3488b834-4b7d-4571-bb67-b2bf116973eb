import { z } from "zod";

export const REQUIRED_MESSAGE = "Esse campo é obrigatório";
export const REQUIRED_ARRAY_MESSAGE = "Selecione pelo menos uma opção";
export const INVALID_EMAIL_MESSAGE = "Email inválido";

export const optionsSchema = z.object({
  label: z.string(),
  value: z.string(),
});

export const optionsSchemaMandatory = z.object(
  {
    label: z.string(),
    value: z.string(),
  },
  { message: REQUIRED_MESSAGE }
);

export const arrayOptionsSchema = z.array(optionsSchema);
export const arrayOptionsSchemaMandatory = z.array(optionsSchema, {
  message: REQUIRED_MESSAGE,
});
