import { getBusinessGroups } from "@/http/business-groups";
import { getBusinessUnits } from "@/http/business-units";
import { getUnions } from "@/http/unions";
import { useQuery } from "@tanstack/react-query";
import {
  UseBillingBatchesQueryProps,
  UseBusinessGroupsQueryProps,
  UseBusinessUnitsQueryProps,
  UseConsultanciesQueryProps,
  UseUnionsQueryProps,
} from "./types";
import { getConsultancies } from "@/http/consultancies";
import { getBillingBatches } from "@/http/billing";

export const useConsultanciesQuery = ({
  filters,
  ...props
}: UseConsultanciesQueryProps) => {
  return useQuery({
    queryKey: ["consultancies", filters],
    queryFn: () =>
      getConsultancies({
        ...filters,
      }),
    ...props,
  });
};

export const useUnionsQuery = ({ filters, ...props }: UseUnionsQueryProps) => {
  return useQuery({
    queryKey: ["unions", filters],
    queryFn: () => getUnions(filters),
    refetchOnWindowFocus: false,
    ...props,
  });
};

export const useBusinessGroupsQuery = ({
  filters,
  ...props
}: UseBusinessGroupsQueryProps) => {
  return useQuery({
    queryKey: ["business-groups", filters],
    queryFn: () => getBusinessGroups({ ...filters }),
    refetchOnWindowFocus: false,
    ...props,
  });
};

export const useBusinessUnitsQuery = ({
  filters,
  ...props
}: UseBusinessUnitsQueryProps) => {
  return useQuery({
    queryKey: ["business-units", filters],
    queryFn: () => getBusinessUnits({ ...filters }),
    refetchOnWindowFocus: false,
    ...props,
  });
};

export const useBillingBatchesQuery = ({
  filters,
  ...props
}: UseBillingBatchesQueryProps) => {
  return useQuery({
    queryKey: ["billing-batches", filters],
    queryFn: () => getBillingBatches({ ...filters }),
    refetchOnWindowFocus: false,
    ...props,
  });
};
