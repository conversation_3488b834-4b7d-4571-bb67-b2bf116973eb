import { GetBatchesResponse } from "@/http/billing/types";
import {
  BusinessGroupResponse,
  IGetBusinessGroups,
} from "@/http/business-groups/types";
import {
  BusinessUnitResponse,
  IGetBusinessUnits,
} from "@/http/business-units/types";
import {
  GetConsultanciesResponse,
  IGetConsultancies,
} from "@/http/consultancies/types";
import { GetUnionsParams, GetUnionsResponse } from "@/http/unions";
import { QueryOptions } from "@tanstack/react-query";

export interface UseConsultanciesQueryProps
  extends QueryOptions<GetConsultanciesResponse> {
  filters?: IGetConsultancies;
  enabled?: boolean;
}

export interface UseUnionsQueryProps extends QueryOptions<GetUnionsResponse> {
  filters?: GetUnionsParams;
  enabled?: boolean;
}

export interface UseBusinessGroupsQueryProps
  extends QueryOptions<BusinessGroupResponse> {
  filters?: IGetBusinessGroups;
  enabled?: boolean;
}

export interface UseBusinessUnitsQueryProps
  extends QueryOptions<BusinessUnitResponse> {
  filters?: IGetBusinessUnits;
  enabled?: boolean;
}

export interface UseBillingBatchesQueryProps
  extends QueryOptions<GetBatchesResponse> {
  filters?: { page?: number; limit?: number };
  enabled?: boolean;
}
