/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEffect, useRef, useState } from "react";

type BatchStatus = "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";

export function useBatchProgressSSE(
  batchId?: string,
  enabled: boolean = true
): {
  progress?: number;
  status?: BatchStatus;
  processedInvoices?: number;
  totalExpectedInvoices?: number;
  failedInvoices?: number;
  totalAmount?: number;
} {
  const [progress, setProgress] = useState<number | undefined>(undefined);
  const [status, setStatus] = useState<BatchStatus | undefined>(undefined);
  const [processedInvoices, setProcessedInvoices] = useState<
    number | undefined
  >(undefined);
  const [totalExpectedInvoices, setTotalExpectedInvoices] = useState<
    number | undefined
  >(undefined);
  const [failedInvoices, setFailedInvoices] = useState<number | undefined>(
    undefined
  );
  const [totalAmount, setTotalAmount] = useState<number | undefined>(undefined);

  // Refs devem ficar no topo do hook (fora do useEffect)
  const attemptRef = useRef(0);
  const retryTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const heartbeatTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastMessageAtRef = useRef<number>(Date.now());

  useEffect(() => {
    if (!enabled || !batchId) return;

    let es: EventSource | null = null;
    let cancelled = false;

    const clearTimers = () => {
      if (retryTimerRef.current) {
        clearTimeout(retryTimerRef.current);
        retryTimerRef.current = null;
      }
      if (heartbeatTimerRef.current) {
        clearTimeout(heartbeatTimerRef.current);
        heartbeatTimerRef.current = null;
      }
    };

    const scheduleHeartbeatCheck = () => {
      if (heartbeatTimerRef.current) clearTimeout(heartbeatTimerRef.current);
      heartbeatTimerRef.current = setTimeout(() => {
        const idleMs = Date.now() - lastMessageAtRef.current;
        // If no messages for 25s, force reconnect
        if (idleMs > 25000) {
          try {
            if (es) es.close();
          } catch {}
          open(true);
          return;
        }
        scheduleHeartbeatCheck();
      }, 10000);
    };

    const scheduleReconnect = () => {
      if (cancelled) return;
      const attempt = ++attemptRef.current;
      const delay = Math.min(30000, 2000 * Math.pow(1.7, attempt - 1));
      if (retryTimerRef.current) clearTimeout(retryTimerRef.current);
      retryTimerRef.current = setTimeout(() => open(true), delay);
    };

    const applyPayload = (raw: unknown) => {
      const payload = (raw || {}) as Record<string, unknown>;
      const type = String(payload?.type || "");

      const processed = Number((payload as any)?.processedInvoices ?? NaN);
      const total = Number((payload as any)?.totalExpectedInvoices ?? NaN);
      const failed = Number((payload as any)?.failedInvoices ?? NaN);
      const amount = Number((payload as any)?.totalAmount ?? NaN);
      const hasCounts = !Number.isNaN(processed) && !Number.isNaN(total);
      const calcFromCounts = hasCounts
        ? Math.round(
            Math.min(100, Math.max(0, (processed / Math.max(1, total)) * 100))
          )
        : undefined;

      const progressValue =
        (payload?.progress !== undefined
          ? Number(payload.progress)
          : calcFromCounts) ?? undefined;

      if (type === "CONNECTION_ESTABLISHED") {
        // não altera progress/status
        return;
      }
      if (type === "PROGRESS_UPDATE") {
        if (progressValue !== undefined) {
          const pct = Math.min(100, Math.max(0, Number(progressValue)));
          setProgress(pct);
        }
        setStatus("PROCESSING");
        if (!Number.isNaN(processed)) setProcessedInvoices(processed);
        if (!Number.isNaN(total)) setTotalExpectedInvoices(total);
        if (!Number.isNaN(failed)) setFailedInvoices(failed);
        if (!Number.isNaN(amount)) setTotalAmount(amount);
        return;
      }
      if (type === "BATCH_COMPLETED" || type === "BATCH_FAILED") {
        setProgress(100);
        setStatus(type === "BATCH_COMPLETED" ? "COMPLETED" : "FAILED");
        if (!Number.isNaN(processed)) setProcessedInvoices(processed);
        if (!Number.isNaN(total)) setTotalExpectedInvoices(total);
        if (!Number.isNaN(failed)) setFailedInvoices(failed);
        if (!Number.isNaN(amount)) setTotalAmount(amount);
        if (es) es.close();
        return;
      }

      // fallback genérico quando não há type, mas há status/progress
      if (progressValue !== undefined) {
        const pct = Math.min(100, Math.max(0, Number(progressValue)));
        setProgress(pct);
      }
      if (payload?.status) {
        const newStatus = String(payload.status) as BatchStatus;
        setStatus(newStatus);
        if (newStatus === "COMPLETED" || newStatus === "FAILED") {
          if (es) es.close();
        }
      }
      if (hasCounts) {
        setProcessedInvoices(processed);
        setTotalExpectedInvoices(total);
      }
      if (!Number.isNaN(failed)) setFailedInvoices(failed);
      if (!Number.isNaN(amount)) setTotalAmount(amount);
    };

    const open = async (isReconnect: boolean = false) => {
      try {
        const { api } = await import("@/services/api");
        const base = api.defaults.baseURL || "";
        const token =
          typeof window !== "undefined"
            ? localStorage.getItem("@gestao-sindicatos:access-token")
            : null;
        const url = `${base}/api/billing/batches/${batchId}/progress${
          token ? `?access_token=${token}` : ""
        }`;
        if (cancelled) return;
        if (es) {
          try {
            es.close();
          } catch {}
        }
        es = new EventSource(url);
        if (!isReconnect) attemptRef.current = 0;
        lastMessageAtRef.current = Date.now();
        scheduleHeartbeatCheck();

        // Fluxo principal: backend envia apenas data: {..., type}
        es.onmessage = (ev) => {
          try {
            applyPayload(JSON.parse(ev.data || "{}"));
            lastMessageAtRef.current = Date.now();
            attemptRef.current = 0;
          } catch {}
        };

        es.onerror = () => {
          // browsers reconectam automaticamente, mas aplicamos backoff/manual
          scheduleReconnect();
        };
      } catch {
        // ignore opening errors
      }
    };

    return () => {
      cancelled = true;
      try {
        if (es) es.close();
      } catch {}
      clearTimers();
    };
  }, [batchId, enabled]);

  return {
    progress,
    status,
    processedInvoices,
    totalExpectedInvoices,
    failedInvoices,
    totalAmount,
  };
}
