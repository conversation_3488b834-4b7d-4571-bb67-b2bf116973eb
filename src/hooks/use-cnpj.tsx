import { ExternalCompanyData, getCompanyByCNPJ } from "@/http/external";
import { useMutation } from "@tanstack/react-query";

interface UseCnpjReturn {
  cnpj: string;
  onSuccess: (data: ExternalCompanyData) => void;
  onError: () => void;
}

export function useCnpj({ cnpj, onSuccess, onError }: UseCnpjReturn) {
  const cleanCnpj = cnpj.replace(/\D/g, "");

  const { mutate, isPending } = useMutation({
    mutationFn: (currentCnpj: string) => getCompanyByCNPJ(currentCnpj),
    onSuccess: onSuccess,
    onError: onError,
  });

  const handleSubmit = () => {
    mutate(cleanCnpj);
  };

  return {
    handleSubmit,
    isPending,
  };
}
