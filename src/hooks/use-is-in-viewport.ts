"use client";

import { useEffect, useRef, useState } from "react";

export function useIsInViewport<T extends HTMLElement>(options?: {
  root?: Element | null;
  rootMargin?: string;
  threshold?: number | number[];
}) {
  const elementRef = useRef<T | null>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    if (!elementRef.current || typeof window === "undefined") return;
    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(Boolean(entry?.isIntersecting)),
      {
        root: options?.root ?? null,
        rootMargin: options?.rootMargin ?? "0px",
        threshold: options?.threshold ?? 0.1,
      }
    );
    observer.observe(elementRef.current);
    return () => observer.disconnect();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    elementRef.current,
    options?.root,
    options?.rootMargin,
    options?.threshold,
  ]);

  return { ref: elementRef, isInViewport: isIntersecting } as const;
}
