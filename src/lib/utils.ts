import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Extrai mensagem amigável de erro de API: concatena errors[].message quando existir
export function extractApiErrorMessage(
  error: unknown,
  fallback = "Erro ao processar a solicitação"
) {
  try {
    const e = error as {
      response?: {
        data?: { message?: string; errors?: Array<{ message?: string }> };
      };
    };
    const list = e?.response?.data?.errors;
    if (Array.isArray(list) && list.length > 0) {
      const combined = list
        .map((it) => (typeof it?.message === "string" ? it.message : ""))
        .filter(Boolean)
        .join(", ");
      if (combined) return combined;
    }
    return e?.response?.data?.message || fallback;
  } catch {
    return fallback;
  }
}

export async function withRetry<T>(
  fn: () => Promise<T>,
  opts?: { retries?: number; baseDelayMs?: number; factor?: number }
): Promise<T> {
  const retries = Math.max(0, opts?.retries ?? 3);
  const base = Math.max(0, opts?.baseDelayMs ?? 500);
  const factor = Math.max(1, opts?.factor ?? 2);
  let lastErr: unknown;
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      return await fn();
    } catch (err) {
      lastErr = err;
      if (attempt === retries) break;
      const delay = Math.min(
        5000,
        Math.round(base * Math.pow(factor, attempt))
      );
      await new Promise((r) => setTimeout(r, delay));
    }
  }
  throw lastErr;
}
