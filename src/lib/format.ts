/**
 * Formata CNPJ para o padrão XX.XXX.XXX/XXXX-XX
 */
export function formatCNPJ(cnpj: string): string {
  if (!cnpj) return "";

  // Remove tudo que não for número
  const numbers = cnpj.replace(/\D/g, "");

  // Se não tiver 14 dígitos, retorna sem formatação
  if (numbers.length !== 14) return cnpj;

  // Aplica a formatação XX.XXX.XXX/XXXX-XX
  return numbers.replace(
    /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
    "$1.$2.$3/$4-$5"
  );
}

/**
 * Remove formatação do CNPJ deixando apenas números
 */
export function unformatCNPJ(cnpj: string): string {
  return cnpj.replace(/\D/g, "");
}

/**
 * Formata CNPJ enquanto digita (máscara)
 */
export function maskCNPJ(value: string): string {
  // Remove tudo que não for número
  const numbers = value.replace(/\D/g, "");

  // Limita a 14 dígitos
  const limited = numbers.substring(0, 14);

  // Aplica formatação progressiva
  if (limited.length <= 2) return limited;
  if (limited.length <= 5) return limited.replace(/(\d{2})(\d+)/, "$1.$2");
  if (limited.length <= 8)
    return limited.replace(/(\d{2})(\d{3})(\d+)/, "$1.$2.$3");
  if (limited.length <= 12)
    return limited.replace(/(\d{2})(\d{3})(\d{3})(\d+)/, "$1.$2.$3/$4");
  return limited.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d+)/, "$1.$2.$3/$4-$5");
}

// Phone (BR) formatting: (11) 98888-7777 or (11) 3888-7777
export function formatPhoneBR(value: string): string {
  const digits = value.replace(/\D/g, "").slice(0, 11);
  if (digits.length <= 10) {
    return digits
      .replace(/(\d{2})(\d)/, "($1) $2")
      .replace(/(\d{4})(\d)/, "$1-$2");
  }
  return digits
    .replace(/(\d{2})(\d)/, "($1) $2")
    .replace(/(\d{5})(\d)/, "$1-$2");
}

export function maskPhoneBR(value: string): string {
  const digits = value.replace(/\D/g, "").slice(0, 11);
  return formatPhoneBR(digits);
}

// CEP formatting: 00000-000
export function formatCEP(value: string): string {
  const digits = value.replace(/\D/g, "").slice(0, 8);
  return digits.replace(/(\d{5})(\d)/, "$1-$2");
}

export function maskCEP(value: string): string {
  return formatCEP(value);
}

// CPF formatting: 000.000.000-00
export function formatCPF(value: string): string {
  const digits = value.replace(/\D/g, "").slice(0, 11);
  if (digits.length !== 11) return value;
  return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
}

export function maskCPF(value: string): string {
  const digits = value.replace(/\D/g, "").slice(0, 11);
  if (digits.length <= 3) return digits;
  if (digits.length <= 6) return digits.replace(/(\d{3})(\d+)/, "$1.$2");
  if (digits.length <= 9)
    return digits.replace(/(\d{3})(\d{3})(\d+)/, "$1.$2.$3");
  return digits.replace(/(\d{3})(\d{3})(\d{3})(\d+)/, "$1.$2.$3-$4");
}

// Dynamic CPF or CNPJ mask based on length
export function maskCpfCnpj(value: string): string {
  const digits = value.replace(/\D/g, "");
  if (digits.length <= 11) return maskCPF(digits);
  return maskCNPJ(digits);
}

// BRL currency formatting helpers
export function formatBRL(value: number): string {
  const numeric = Number.isFinite(value as unknown as number)
    ? (value as unknown as number)
    : 0;
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(numeric);
}

export function parseBRLToNumber(input: string): number {
  const digits = (input || "").replace(/\D/g, "");
  const num = Number(digits) / 100;
  return Number.isFinite(num) ? num : 0;
}

// Role translation helper (backend roles -> PT-BR labels)
export function translateUserRole(role?: string): string {
  const map: Record<string, string> = {
    ADMIN_CLIENT: "Administrador Cliente",
    ADMIN_SDG: "Administrador SDG",
    ATTENDANT_SDG: "Atendente SDG",
    ACCOUNTANT: "Contador",
    CONTROLLER_SDG: "Controlador SDG",
    FINANCIAL_CLIENT: "Financeiro Cliente",
    FINANCIAL_SDG: "Financeiro SDG",
  };
  return role && map[role] ? map[role] : role || "";
}
